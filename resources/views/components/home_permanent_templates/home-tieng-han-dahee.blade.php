@extends('layouts.default_tieng_han_dahee')
@section('content')


<header class="header bg-gradient-to-r from-[#406BF2] to-[#003EB0] lg:bg-none">
    <div class="container mx-auto !px-4 xl:max-w-6xl xl:!px-0">
        <nav class="flex items-center justify-between !py-1 lg:!py-3">
            <!-- Logo -->
            <div class="flex items-center">
                <img
                    src="{{ asset('assets/frontend/dahee/assets/images/logo.png') }}"
                    alt="DAHEE Education"
                    class="xl:w-[98px] h-auto w-[66px]"
                />
            </div>

            <div class="flex gap-x-6 lg:gap-x-11">
                <!-- Desktop Navigation -->
                <div
                    class="hidden lg:flex items-center gap-10 text-white text-lg leading-[1.7]"
                >
                    <a
                        href="#about"
                        class="text-inherit hover:text-blue-100 transition-colors duration-200 font-light"
                    >
                        Giớ<PERSON> thiệu
                    </a>
                    <a
                        href="#course-list"
                        class="text-inherit hover:text-blue-100 transition-colors duration-200 font-light"
                    >
                        Danh sách khóa học
                    </a>
                    <a
                        href="#teachers"
                        class="text-inherit hover:text-blue-100 transition-colors duration-200 font-light"
                    >
                        Đội ngũ
                    </a>
                    <a
                        href="#form-register"
                        class="text-inherit hover:text-blue-100 transition-colors duration-200 font-light"
                    >
                        Liên hệ
                    </a>
                </div>

                <!-- CTA Button / User Authentication -->
                <div class="flex items-center relative">
                    @if(!Auth()->check())
                        <a
                            class="cta-button duration-300 flex items-center justify-center transform bg-gradient-to-l text-[#003EB0] from-[#bff2ff] via-white to-[#fffbab] rounded-full font-semibold border-l-2 border-r-2 border-b-2 border-[#eefbff] hover:from-blue-100 hover:to-cyan-100 hover:scale-105"
                            data-bs-toggle="modal"
                            data-bs-target="#modal-auth"
                        >
                            <div class="w-auto h-full aspect-square absolute top-0 -left-0.5">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/comments.svg') }}"
                                    width="48"
                                    height="48"
                                    alt=""
                                    class="w-full h-full object-cover"
                                />
                            </div>
                            <span
                                class="text-base leading-relaxed lg:text-xl lg:leading-10 font-semibold"
                                >NHẬN TƯ VẤN</span
                            >
                        </a>
                    @else
                        <!-- User Profile Dropdown -->
                        <div class="relative">
                            <button
                                class="lg:flex hidden bg-white border border-gray-200 text-gray-700 !py-1.5 pl-1.5 pr-4 rounded-full items-center hover:bg-gray-50 transition-colors duration-200"
                                id="user-menu-button"
                                aria-expanded="false"
                                data-bs-toggle="dropdown"
                            >
                                <i class="bi bi-person-circle text-2xl mr-2 {{ Auth()->user()->photo ? 'hidden' : '' }}"></i>
                                @if(Auth()->user()->photo)
                                    <img
                                        src="{{ get_image(Auth()->user()->photo) }}"
                                        alt="User Avatar"
                                        width="30"
                                        height="30"
                                        class="w-[30px] h-[30px] mr-2 object-cover object-center rounded-full"
                                    />
                                @endif
                                <span class="text-sm font-medium">{{ ucfirst(Auth()->user()->name) }}</span>
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div
                                class="dropdown-menu block absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 !py-2 z-50 hidden"
                                id="user-dropdown">
                                <!-- User Info Header -->
                                <div class="!px-4 !py-3 border-b border-gray-100">
                                    <div class="flex items-center">
                                        @if(Auth()->user()->photo)
                                            <img
                                                src="{{ get_image(Auth()->user()->photo) }}"
                                                alt="User Avatar"
                                                class="w-10 h-10 rounded-full object-cover mr-3"
                                            />
                                        @else
                                            <i class="bi bi-person-circle text-4xl mr-3"></i>
                                        @endif
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
                                            <p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
                                            @php
                                                $enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
                                            @endphp
                                            @if($enrollment_status == 'valid')
                                                <span
                                                    class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path
                                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                    PRO
                                                </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
                                                    FREE
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                                    <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                        </svg>
                                        {{ get_phrase('Dashboard') }}
                                    </a>
                                @endif

                                @if (Auth()->user()->role != 'admin')
                                    <a href="{{ route('my.courses') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        {{ get_phrase('My Courses') }}
                                    </a>

                                    <a href="{{ route('my.profile') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        {{ get_phrase('My Profile') }}
                                    </a>

                                    <a href="{{ route('my.affiliate') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        {{ get_phrase('Affiliate') }}
                                    </a>

                                    <a href="{{ route('wishlist') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        {{ get_phrase('Wishlist') }}
                                    </a>

                                    <a href="{{ route('message') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        {{ get_phrase('Message') }}
                                    </a>

                                    <a href="{{ route('purchase.history') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ get_phrase('Purchase History') }}
                                    </a>
                                @endif

                                <div class="border-t border-gray-100 mt-2 !pt-2">
                                    <a href="{{ route('logout.course') }}"
                                        class="flex items-center !px-4 !py-2 text-sm text-red-600 hover:bg-red-50">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                        {{ get_phrase('Log Out') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Mobile Menu Button -->
                <div class="flex items-center lg:hidden">
                    <button
                        id="mobile-menu-button"
                        class="text-white hover:text-blue-100 focus:outline-none"
                    >
                        <svg
                            class="w-9 h-9"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"
                            ></path>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="hidden lg:hidden pb-4 mt-2">
            <div class="flex flex-col space-y-! text-white text-base font-medium">
                <a
                    href="#about"
                    class="[font:inherit] hover:text-blue-100 transition-colors duration-200 py-2"
                >
                    Giới thiệu
                </a>
                <a
                    href="#courses"
                    class="[font:inherit] hover:text-blue-100 transition-colors duration-200 py-2"
                >
                    Danh sách khóa học
                </a>
                <a
                    href="#teachers"
                    class="[font:inherit] hover:text-blue-100 transition-colors duration-200 py-2"
                >
                    Đội ngũ
                </a>
                <a
                    href="#form-register"
                    class="[font:inherit] hover:text-blue-100 transition-colors duration-200 py-2"
                >
                    Liên hệ
                </a>

                @if(!Auth()->check())
                    <button
                        class="text-base lg:w-40 2xl:w-[188px] h-10 bg-gradient-to-b from-[#406BF2] to-[#003EB0] hover:bg-blue-600 text-white rounded-full font-bold transition-colors"
                        data-bs-toggle="modal"
                        data-bs-target="#modal-auth"
                    >
                        ĐĂNG NHẬP
                    </button>
                @else
                    <!-- Mobile User Profile -->
                    <div class="border-t !pt-3 mt-3">
                        <div class="flex items-center mb-3">
                            @if(Auth()->user()->photo)
                                <img
                                    src="{{ get_image(Auth()->user()->photo) }}"
                                    alt="User Avatar"
                                    class="w-10 h-10 rounded-full object-cover mr-3"
                                />
                            @else
                                <i class="bi bi-person-circle text-4xl mr-3"></i>
                            @endif
                            <div>
                                <p class="text-sm font-medium text-white">{{ ucfirst(Auth()->user()->name) }}</p>
                                <p class="text-xs text-blue-100">{{ Auth()->user()->email }}</p>
                            </div>
                        </div>

                        @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                            <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                                class="block !py-2 text-sm text-white hover:text-blue-100">
                                {{ get_phrase('Dashboard') }}
                            </a>
                        @endif

                        @if (Auth()->user()->role != 'admin')
                            <a href="{{ route('my.courses') }}"
                                class="block !py-2 text-sm text-white hover:text-blue-100">
                                {{ get_phrase('My Courses') }}
                            </a>
                            <a href="{{ route('my.profile') }}"
                                class="block !py-2 text-sm text-white hover:text-blue-100">
                                {{ get_phrase('My Profile') }}
                            </a>
                            <a href="{{ route('wishlist') }}"
                                class="block !py-2 text-sm text-white hover:text-blue-100">
                                {{ get_phrase('Wishlist') }}
                            </a>
                            <a href="{{ route('purchase.history') }}"
                                class="block !py-2 text-sm text-white hover:text-blue-100">
                                {{ get_phrase('Purchase History') }}
                            </a>
                        @endif

                        <a href="{{ route('logout.course') }}"
                            class="block !py-2 text-sm text-red-400 hover:text-red-300 border-t !pt-2 mt-2">
                            {{ get_phrase('Log Out') }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</header>

<!-- Main Content -->
<main>
    <!-- Hero Section -->
    <section id="hero" class="hero relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0">
            <img
                src="{{ asset('assets/frontend/dahee/assets/images/hero-bg.png') }}"
                alt="Background"
                class="w-full h-full object-cover object-bottom hidden lg:block"
            />
            <img
                src="{{ asset('assets/frontend/dahee/assets/images/banner-bg-mobile.png') }}"
                alt="Background"
                class="w-full h-full object-cover object-bottom block lg:hidden"
            />
        </div>

        <!-- Main Content Container -->
        <div class="relative z-10 container mx-auto !px-4 xl:max-w-6xl xl:!px-0 xl:pt-32">
            <div class="grid lg:grid-cols-2 gap-12 xl:gap-7 items-center w-full">
                <div class="relative !order-2 lg:!order-1" data-x-aos="fade-right" data-aos-duration="1000">
                    <div class="relative z-10">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/hero-left.png') }}"
                            alt="Korean Teacher"
                            class="w-full max-w-lg mx-auto object-contain"
                        />
                    </div>
                </div>

                <div
                    class="text-white text-center flex flex-col items-center !order-1 lg:!order-2"
                >
                    <!-- Main Title -->
                    <h1
                        class="text-[58px] lg:text-6xl xl:text-[86px] font-semibold leading-[1.1] relative xl:mb-[18px]"
                        data-x-aos="fade-down" data-aos-duration="1000" data-aos-delay="200"
                    >
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/hero-title-bg.svg') }}"
                            width="136px"
                            height="128px"
                            alt=""
                            class="h-20 lg:h-32 w-auto object-cover absolute right-0 top-0 translate-x-1/2 -translate-y-1/4"
                        />
                        <span class="relative z-10 block">Tiếng Hàn</span>
                        <span class="relative z-10 block">Dahee</span>
                    </h1>

                    <!-- Title Background -->
                    <div
                        class="mt-3 lg:mt-0 w-full md:w-auto relative bg-gradient-to-l from-[#bff2ff] via-white to-[#fffbab] text-blue-900 px-3 py-2 rounded-lg font-bold xl:mb-4 text-lg lg:text-xl xl:text-[32px] xl:leading-snug"
                        data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="400"
                    >
                        <span class="gradient-text w-full">
                            Tiếng Hàn không khó vì có Dahee!
                        </span>
                    </div>

                    <!-- Description -->
                    <p
                        class="!text-[15px] md:!text-lg leading-5 lg:!text-[22px] xl:leading-snug text-white font-medium !mb-4 !mt-2.5 lg:!mb-5 md:!mt-3 lg:!mt-0"
                        data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="600"
                    >
                        Dahee – nơi học thật, hiểu thật và được lắng nghe thật!
                    </p>

                    <!-- Student Avatars -->
                    <div class="flex items-center space-x-4 mb-6" data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="800">
                        <div class="flex -space-x-5">
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-1.png') }}"
                                alt="Student 1"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-2.png') }}"
                                alt="Student 2"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-3.png') }}"
                                alt="Student 3"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-4.png') }}"
                                alt="Student 4"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />

                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-5.png') }}"
                                alt="Student 5"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />

                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/student-6.png') }}"
                                alt="Student 6"
                                width="50"
                                height="50"
                                class="w-[50px] aspect-square rounded-full object-cover"
                            />
                        </div>
                        <div class="font-bold text-center text-[#BFF2FF]">
                            <div class="text-2xl">10,000+</div>
                            <div class="text-sm">HỌC VIÊN</div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="flex items-center relative" data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="1000">
                        <a
                            href="#course-list"
                            class="hero-cta-btn text-xl leading-relaxed duration-300 flex items-center justify-center transform bg-gradient-to-r text-[#003EB0] from-[#FFF7E3] to-[#FFCA43] rounded-full font-semibold border-l-2 border-r-2 border-b-2 border-[#eefbff] hover:from-[#FFCA43] hover:to-[#FFCA43] hover:scale-105"
                        >
                            <div
                                class="w-auto h-full aspect-square absolute top-0 -left-0.5"
                            >
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/graduate.svg') }}"
                                    width="48"
                                    height="48"
                                    alt=""
                                    class="w-full h-full object-cover"
                                />
                            </div>
                            <span>HỌC THỬ MIỄN PHÍ</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="about bg-white xl:pt-20 xl:pb-15 !pt-6">
        <!-- Top Section - Introduction -->
        <div class="container mx-auto xl:max-w-6xl xl:!px-0">
            <div class="flex items-center !gap-5 md:!gap-8 lg:!gap-16 flex-col lg:flex-row">
                <!-- Left Content -->
                <div class="xl:w-full" data-x-aos="fade-right" data-aos-duration="1000">
                    <!-- Section Tag -->
                    <div
                        class="section-tag flex text-2xl leading-[1.4] !mb-1 items-center gap-x-2.5 xl:!mb-4 text-[#3B92FF] font-medium"
                        data-x-aos="fade-down" data-aos-duration="800" data-aos-delay="200"
                    >
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/carret-left.svg') }}"
                            alt=""
                            width="12"
                            height="14"
                        />

                        <span>Giới thiệu</span>
                    </div>

                    <!-- Main Title -->
                    <h2
                        class="text-[32px] lg:text-4xl xl:text-[52px] xl:leading-tight font-semibold gradient-text !mb-4"
                        data-x-aos="fade-up" data-aos-duration="1000" data-aos-delay="300"
                    >
                        Tiếng Hàn Dahee
                    </h2>

                    <!-- Description -->
                    <div class="space-y-4 text-justify text-base text-[#313131]" data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                        <p>
                            <strong>
                                Tiếng Hàn Dahee là trung tâm đào tạo tiếng Hàn online chuyên
                                sâu,
                            </strong>
                            cung cấp lộ trình học tiếng Hàn từ cơ bản đến nâng cao, phù hợp
                            cho người đi làm, sinh viên và học sinh có nhu cầu
                            <strong>
                                luyện thi Trung học phổ thông (THPT); TOPIK hoặc du học Hàn
                                Quốc.
                            </strong>
                        </p>
                        <p>
                            Đồng hành cùng bạn là đội ngũ giảng viên chất lượng cao,
                            <strong>100% đạt TOPIK 5-6</strong>, tốt nghiệp chuyên ngành
                            Ngôn ngữ Hàn tại các trường đại học uy tín của Việt Nam và Hàn
                            Quốc (trình độ cử nhân, thạc sĩ,...) với chuyên môn vững vàng,
                            giàu kinh nghiệm và luôn tận tâm với từng học viên.
                        </p>
                        <p>
                            Với hệ thống khóa học đa dạng từ sơ cấp, trung cấp đến luyện thi
                            TOPIK 1-6, chương trình học tại Dahee được thiết kế bài bản, lộ
                            trình rõ ràng theo từng mục tiêu:
                        </p>
                    </div>

                    <!-- Feature Cards -->
                    <div
                        class="text-[15px] leading-5 grid md:grid-cols-3 gap-1.5 !mt-5 text-[#313131] font-semibold"
                        data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="600"
                    >
                        <div
                            class="feature-card flex items-center lg:items-start gap-x-2.5 bg-[#DCEBFF] rounded-2xl text-center interactive-element !mb-2 lg:!mb-0"
                            data-x-aos="zoom-in" data-aos-duration="600" data-aos-delay="700"
                        >
                            <div class="w-10 h-10 rounded-full shrink-0 overflow-hidden">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/book.svg') }}"
                                    alt="Book"
                                    class="w-full h-full object-cover"
                                />
                            </div>
                            <p class="text-left">
                                Luyện thi TOPIK hiệu quả với giáo trình độc quyền
                            </p>
                        </div>
                        <div
                            class="feature-card flex items-center lg:items-start gap-x-2.5 bg-[#DCEBFF] rounded-2xl text-center interactive-element !mb-2 lg:!mb-0"
                            data-x-aos="zoom-in" data-aos-duration="600" data-aos-delay="800"
                        >
                            <div class="w-10 h-10 rounded-full shrink-0 overflow-hidden">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/chat.svg') }}"
                                    alt="Chat"
                                    class="w-full h-full object-cover"
                                />
                            </div>

                            <p class="text-left">
                                Giao tiếp tiếng Hàn thực chiến từ cơ bản đến nâng cao
                            </p>
                        </div>
                        <div
                            class="feature-card flex items-center lg:items-start gap-x-2.5 bg-[#DCEBFF] rounded-2xl text-center interactive-element !mb-2 lg:!mb-0"
                            data-x-aos="zoom-in" data-aos-duration="600" data-aos-delay="900"
                        >
                            <div class="w-10 h-10 rounded-full shrink-0 overflow-hidden">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/graduate.svg') }}"
                                    alt="Graduate"
                                    class="w-full h-full object-cover"
                                />
                            </div>

                            <p class="text-left">
                                Khoá học theo cấp độ: Sơ cấp 1-2, Trung cấp 1, Ôn TOPIK 1–6
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Right Content - Image -->
                <div class="relative lg:w-[448px] shrink-0" data-x-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/about-right.png') }}"
                        alt="Korean Learning"
                        width="448px"
                        height="524px"
                        class="w-full object-contain"
                    />
                </div>
            </div>

            <!-- Bottom Section - CTA -->
            <div class="mt-10 lg:mt-18 pb-8 lg:pb-0" data-x-aos="fade-up" data-aos-duration="1000" data-aos-delay="300">
                <div
                    class="cta-section rounded-[30px] relative overflow-hidden bg-right-bottom bg-cover"
                    style="background-image: url({{ asset('assets/frontend/dahee/assets/images/about-bottom-bg.png') }})"
                >
                    <!-- Background Pattern -->

                    <div class="relative z-10">
                        <div class="flex xl:gap-9 flex-col lg:flex-row">
                            <div class="teacher-image relative z-10 xl:w-[460px] shrink-0" data-x-aos="slide-right" data-aos-duration="800" data-aos-delay="400">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/about-bottom.png') }}"
                                    alt="Korean Teacher with TOPIK Book"
                                    width="460px"
                                    height="303px"
                                    class="w-full lg:max-w-[460px] object-cover lg:h-auto !h-[300px] lg:!h-full"
                                />
                            </div>

                            <div
                                class="text-white xl:!pt-[46px] xl:!pr-[41px] xl:!pb-[69px] !p-4"
                                data-x-aos="slide-left" data-aos-duration="800" data-aos-delay="500"
                            >
                                <h3
                                    class="cta-title text-[22px] lg:text-[28px] leading-8 font-semibold xl:leading-10 xl:mb-[13px]"
                                >
                                    Tiếng Hàn không khó – Chỉ cần học đúng cách
                                </h3>

                                <div
                                    class="cta-badge mt-3 lg:my-0 text-[20px] lg:text-[28px] font-semibold xl:leading-10 !mb-3 px-6 py-0 bg-gradient-to-l text-[#0057E4] from-[#bff2ff] via-white to-[#fffbab] inline-block"
                                >
                                    Học cùng Dahee!
                                </div>

                                <p
                                    class="text-white font-normal text-base lg:text-xl leading-[1.3] mt-2"
                                >
                                    Dù bạn muốn học để thi THPT, đi du học, hay làm việc tại
                                    công ty Hàn – Dahee luôn có khóa học phù hợp để bạn bắt
                                    đầu và vững vàng để đạt được mục tiêu.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Combo Section -->
    <section
        id="courses"
        class="course-combo pt-6 pb-28 xl:pt-18 xl:pb-44 bg-gradient-to-b from-[#EBF3FF] to-[#C4DBFF]"
    >
        <div class="container mx-auto px-4 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-6 lg:mb-12" data-x-aos="fade-down" data-aos-duration="1000">
                <h2
                    class="combo-title combo-highlight gradient-text text-[32px] lg:text-4xl xl:text-[52px] leading-[1.1] w-fit mx-auto font-semibold !mb-5 lg:!mb-4 xl:!mb-7 after:w-28 xl:after:w-40 after:-bottom-2"
                    data-x-aos="fade-up" data-aos-duration="1000" data-aos-delay="200"
                >
                    Khóa học theo Combo
                </h2>
                <p
                    class="combo-subtitle text-[#313131] xl:w-[737px] text-center text-xl leading-[1.3] font-normal mx-auto xl:mb-11"
                    data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="400"
                >
                    Phù hợp cho học viên muốn học bài bản, tiết kiệm chi phí, có lộ trình rõ
                    ràng từ sơ cấp đến trung cấp, luyện thi TOPIK.
                </p>
            </div>

            <!-- Course Cards Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="600">
                <!-- Combo 1 - TOPIK 2 -->
                <div
                    class="combo-card group transition-all duration-300 transform hover:-translate-y-2"
                    data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="200"
                >
                    <!-- Card Header -->
                    <div
                        class="combo-badge w-[166px] h-8 text-xl leading-[1.1] bg-[#0057E4] mx-auto text-center text-white px-4 py-1 rounded-t-full font-medium block"
                    >
                        COMBO
                    </div>

                    <!-- Card Body -->
                    <div
                        class="!px-5 pt-7 pb-6 lg:!p-6 bg-white rounded-[30px] shadow-lg group-hover:shadow-xl"
                    >
                        <h3
                            class="combo-card-title text-center mb-2 text-[#313131] font-bold text-xl leading-[1.1]"
                        >
                            LỘ TRÌNH HỌC TỪ 0 ĐẾN<br class="hidden lg:block" />
                            TOPIK 2
                        </h3>
                        <!-- Price -->
                        <div
                            class="combo-price flex items-center justify-center gap-x-1.5 text-center mb-[18px]"
                        >
                            <div
                                class="price-main text-[28px] leading-[1.22] font-bold gradient-text"
                            >
                                6.999K
                            </div>
                            <div
                                class="price-sessions w-[123px] text-lg leading-[23px] shrink-0 text-[#003EB0] pl-1 py-px text-left font-medium inline-block"
                            >
                                80 buổi học
                            </div>
                        </div>

                        <!-- Features -->
                        <div
                            class="combo-features xl:text-base text-[#313131] font-normal !px-4 !py-3 bg-gradient-to-b from-[#DCEBFF] to-white rounded-[20px] mb-1.5"
                        >
                            <h4 class="!font-semibold [font:inherit] !mb-2">
                                Combo bao gồm
                            </h4>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Sơ cấp 1</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Sơ cấp 2</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Ôn thi TOPIK 2</span>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="#form-register"
                            class="block text-center combo-cta-btn text-lg leading-[1.23] py-2.5 w-full hover:!bg-[#0057E4] text-[#0057E4] rounded-full font-medium !bg-white hover:text-white transition-all duration-300"
                        >
                            Đăng ký học thử
                        </a>
                    </div>
                </div>

                <!-- Combo 2 - TOPIK 3 -->
                <div
                    class="combo-card group transition-all duration-300 transform hover:-translate-y-2"
                    data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="400"
                >
                    <!-- Card Header -->
                    <div
                        class="combo-badge w-[166px] h-8 text-xl leading-[1.1] bg-[#0057E4] mx-auto text-center text-white px-4 py-1 rounded-t-full font-medium block"
                    >
                        COMBO
                    </div>

                    <!-- Card Body -->
                    <div
                        class="!px-5 pt-7 pb-6 lg:!p-6 bg-white rounded-[30px] shadow-lg group-hover:shadow-xl"
                    >
                        <h3
                            class="combo-card-title text-center mb-2 text-[#313131] font-bold text-xl leading-[1.1]"
                        >
                            LỘ TRÌNH HỌC TỪ 0 ĐẾN<br class="hidden lg:block" />
                            TOPIK 3
                        </h3>
                        <!-- Price -->
                        <div
                            class="combo-price flex items-center justify-center gap-x-1.5 text-center mb-[18px]"
                        >
                            <div
                                class="price-main text-[28px] leading-[1.22] font-bold gradient-text"
                            >
                                9.250k
                            </div>
                            <div
                                class="price-sessions w-[123px] text-lg leading-[23px] shrink-0 text-[#003EB0] pl-1 py-px text-left font-medium inline-block"
                            >
                                100 buổi học
                            </div>
                        </div>

                        <!-- Features -->
                        <div
                            class="combo-features xl:text-base text-[#313131] font-normal !px-4 !py-3 bg-gradient-to-b from-[#DCEBFF] to-white rounded-[20px] mb-1.5"
                        >
                            <h4 class="!font-semibold [font:inherit] !mb-2">
                                Combo bao gồm
                            </h4>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2"
                                />
                                <span>Sơ cấp 1</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />

                                <span>Sơ cấp 2</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />

                                <span>Ôn thi TOPIK 3,4</span>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="#form-register"
                            class="block text-center combo-cta-btn text-lg leading-[1.23] py-2.5 w-full hover:!bg-[#0057E4] text-[#0057E4] rounded-full font-medium !bg-white hover:text-white transition-all duration-300"
                        >
                            Đăng ký học thử
                        </a>
                    </div>
                </div>

                <!-- Combo 3 - TOPIK 4 -->
                <div
                    class="combo-card group transition-all duration-300 transform hover:-translate-y-2"
                    data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="600"
                >
                    <!-- Card Header -->
                    <div
                        class="combo-card group transition-all duration-300 transform hover:-translate-y-2"
                    >
                        <!-- Card Header -->
                        <div
                            class="combo-badge w-[166px] h-8 text-xl leading-[1.1] bg-[#0057E4] mx-auto text-center text-white px-4 py-1 rounded-t-full font-medium block"
                        >
                            COMBO
                        </div>
                    </div>

                    <!-- Card Body -->
                    <div
                        class="!px-5 pt-7 pb-6 lg:!p-6 bg-white rounded-[30px] shadow-lg group-hover:shadow-xl"
                    >
                        <h3
                            class="combo-card-title text-center mb-2 text-[#313131] font-bold text-xl leading-[1.1]"
                        >
                            LỘ TRÌNH HỌC TỪ 0 ĐẾN<br class="hidden lg:block" />
                            TOPIK 4
                        </h3>
                        <!-- Price -->
                        <div
                            class="combo-price flex items-center justify-center gap-x-1.5 text-center mb-[18px]"
                        >
                            <div
                                class="price-main text-[28px] leading-[1.22] font-bold gradient-text"
                            >
                                11.999K
                            </div>
                            <div
                                class="price-sessions w-[123px] text-lg leading-[23px] shrink-0 text-[#003EB0] pl-1 py-px text-left font-medium inline-block"
                            >
                                130 buổi học
                            </div>
                        </div>

                        <!-- Features -->
                        <div
                            class="combo-features xl:text-base text-[#313131] font-normal !px-4 !py-3 bg-gradient-to-b from-[#DCEBFF] to-white rounded-[20px] mb-1.5"
                        >
                            <h4 class="!font-semibold [font:inherit] !mb-2">
                                Combo bao gồm
                            </h4>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2"
                                />
                                <span>Sơ cấp 1</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2"
                                />
                                <span>Sơ cấp 2</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2"
                                />
                                <span>Ôn thi TOPIK 4</span>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="#form-register"
                            class="block text-center combo-cta-btn text-lg leading-[1.23] py-2.5 w-full hover:!bg-[#0057E4] text-[#0057E4] rounded-full font-medium !bg-white hover:text-white transition-all duration-300"
                        >
                            Đăng ký học thử
                        </a>
                    </div>
                </div>

                <!-- Combo 4 - TOPIK 5,6 -->
                <div
                    class="combo-card group transition-all duration-300 transform hover:-translate-y-2"
                    data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="800"
                >
                    <!-- Card Header -->
                    <div
                        class="combo-badge w-[166px] h-8 text-xl leading-[1.1] bg-[#0057E4] mx-auto text-center text-white px-4 py-1 rounded-t-full font-medium block"
                    >
                        COMBO
                    </div>

                    <!-- Card Body -->
                    <div
                        class="!px-5 pt-7 pb-6 lg:!p-6 bg-white rounded-[30px] shadow-lg group-hover:shadow-xl"
                    >
                        <h3
                            class="combo-card-title text-center mb-2 text-[#313131] font-bold text-xl leading-[1.1]"
                        >
                            LỘ TRÌNH HỌC TỪ 0 ĐẾN<br class="hidden lg:block" />
                            TOPIK 5,6
                        </h3>
                        <!-- Price -->
                        <div
                            class="combo-price flex items-center justify-center gap-x-1.5 text-center mb-[18px]"
                        >
                            <div
                                class="price-main text-[28px] leading-[1.22] font-bold gradient-text"
                            >
                                15.999K
                            </div>
                            <div
                                class="price-sessions w-[123px] text-lg leading-[23px] shrink-0 text-[#003EB0] pl-1 py-px text-left font-medium inline-block"
                            >
                                160 buổi học
                            </div>
                        </div>

                        <!-- Features -->
                        <div
                            class="combo-features xl:text-base text-[#313131] font-normal !px-4 !py-3 bg-gradient-to-b from-[#DCEBFF] to-white rounded-[20px] mb-1.5"
                        >
                            <h4 class="!font-semibold [font:inherit] !mb-2">
                                Combo bao gồm
                            </h4>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2"
                                />
                                <span>Sơ cấp 1</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Sơ cấp 2</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Trung cấp 1</span>
                            </div>
                            <div class="feature-item flex items-center">
                                <img
                                    src="{{ asset('assets/frontend/dahee/assets/images/check-circle.svg') }}"
                                    alt="Check"
                                    class="w-4 h-4 mr-2 text-blue-500"
                                />
                                <span>Ôn thi TOPIK 5,6</span>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="#form-register"
                            class="block text-center combo-cta-btn text-lg leading-[1.23] py-2.5 w-full hover:!bg-[#0057E4] text-[#0057E4] rounded-full font-medium !bg-white hover:text-white transition-all duration-300"
                        >
                            Đăng ký học thử
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="course-list"></div>
    </section>

    <section
        class="course-list bg-white pb-5 lg:pb-24 bg-no-repeat bg-cover bg-center"
        style="background-image: url({{ asset('assets/frontend/dahee/assets/images/course-list-bg.svg') }})"
    >
        <div class="container mx-auto xl:max-w-6xl xl:px-0 relative z-10">
            <!-- Section Header -->
            <div
                class="text-center bg-white xl:pt-11 pb-5 rounded-t-[30px] !pt-4 md:!pt-6 -translate-y-[90px]"
            >
                <h2
                    class="course-title combo-highlight xl:mb-2.5 after:w-[108px] md:after:w-[87px] md:mb-4 after:-bottom-2 after:-translate-x-2/3 text-[32px] lg:text-4xl xl:text-[52px] xl:leading-[1.1] gradient-text font-semibold w-fit mx-auto"
                >
                    Danh sách Khóa học lẻ
                </h2>
                <p class="course-subtitle text-xl text-[#313131]">
                    Các khóa học tiếng Hàn theo lộ trình chuẩn TOPIK
                </p>
            </div>

            <!-- Course Cards Grid -->
            <div
                class="grid md:grid-cols-2 lg:grid-cols-3 -mt-22 gap-x-4 xl:gap-x-8 gap-y-[30px]"
            >

                @if(isset($courses) && $courses->count()>0)
                    @foreach($courses as $course)
                        <div
                            class="!pt-3 !px-6 !pb-7 course-card xl:!p-4 bg-white rounded-[20px] xl:rounded-[30px] overflow-hidden transition-all duration-300 transform hover:-translate-y-2"
                        >
                            <!-- Card Header -->
                            <div class="course-header bg-white text-center mb-[18px]">
                                <div
                                    class="course-icon w-[52px] h-[52px] flex items-center justify-center mx-auto !mb-1"
                                >
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/graduate-hat 1.svg') }}"
                                        alt="Graduate Hat"
                                        class="w-full h-full object-cover"
                                    />
                                </div>
                                <h3
                                    class="course-card-title font-bold text-xl leading-[1.1] text-[#313131] !mb-3"
                                >
                                    {{$course->title}}
                                </h3>

                                <!-- Price -->
                                <div
                                    class="course-price flex items-center justify-center gap-x-1.5 text-center mb-1"
                                >
                                    <div
                                        class="price-main text-[28px] leading-[1.22] font-bold gradient-text"
                                    >
                                        {{ number_format($course->price/1000, 0, ',', '.') }}k
                                    </div>
                                    <div
                                        class="price-sessions w-[123px] text-lg leading-[23px] shrink-0 text-[#003EB0] pl-1 py-px text-left font-medium inline-block"
                                    >
                                        30 buổi học
                                    </div>
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div>
                                <!-- Description -->
                                <div
                                    class="course-description xl:px-6 xl:pt-3 xl:pb-6 !p-3 rounded-[20px] bg-gradient-to-b from-[#DCEBFF] to-white mb-6 text-left"
                                >
                                    <p class="text-[#313131] text-base">
                                        <strong>Dành cho:</strong> {{$course->short_description}}
                                    </p>
                                </div>

                                <!-- CTA Button -->
                                <a href="{{ route('course.details', $course->slug) }}"
                                    class="block text-center course-cta-btn py-2.5 w-full hover:!bg-[#0057E4] text-[#0057E4] rounded-full font-medium !bg-white hover:text-white transition-all duration-300"
                                >
                                    Đăng ký học thử
</a>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </section>

    <section
        id="reason"
        class="reason bg-white pb-6 lg:pb-22"
        style="
            background-image: url('{{ asset('assets/frontend/dahee/assets/images/bg-why.png') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        "
    >
        <div class="container xl:max-w-6xl mx-auto !px-4 md:!px-8 xl:!px-0">
            <!-- Main Heading -->
            <h1
                class="text-center !mb-5 xl:!mb-[52px] font-semibold text-[32px] xl:text-[52px] leading-[1.1] gradient-text"
                data-x-aos="fade-down" data-aos-duration="800"
            >
                Vì sao nên học tiếng Hàn<br />online tại Dahee?
            </h1>

            <!-- Two-column Grid -->
            <div class="flex flex-col lg:flex-row gap-8" data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <!-- Left Column -->
                <div class="w-full lg:w-1/2 flex-auto space-y-6">
                    <!-- Block 1: Lộ trình rõ ràng -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-right" data-aos-duration="800" data-aos-delay="400">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/book-reader.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Lộ trình rõ ràng – Học đúng mục tiêu, không lan man
                            </h2>
                        </div>
                        <ul
                            class="reason-list list-disc list-outside pl-4 text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <li>
                                Mỗi học viên đều được xây dựng lộ trình học cụ thể từ đầu,
                                biết rõ mình cần học gì – khi nào.
                            </li>
                            <li>
                                Đủ mục tiêu của bạn là giao tiếp, thi TOPIK hay du học Dahee
                                đều có combo khóa học phù hợp, giúp tiết kiệm thời gian và
                                không bị lạc quãng giữa chừng.
                            </li>
                        </ul>
                    </div>

                    <!-- Block 2: Giáo viên giỏi -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-right" data-aos-duration="800" data-aos-delay="600">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/teacher.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Giáo viên giỏi – Theo sát từng học viên
                            </h2>
                        </div>
                        <ul
                            class="reason-list list-disc list-outside pl-4 text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <li>
                                100% giáo viên đạt TOPIK 5–6, tốt nghiệp chuyên ngành Ngôn
                                ngữ Hàn, có bằng Cử nhân & Thạc sĩ tại Hàn Quốc.
                            </li>
                            <li>
                                1 lớp có tới 3 giáo viên hỗ trợ: đứng lớp – chữa bài viết và
                                nói – tổng ôn.
                            </li>
                            <li>
                                Phát âm chuẩn tiếng Seoul, tận tâm và có kinh nghiệm phiên
                                dịch, giảng dạy chuyên sâu.
                            </li>
                        </ul>
                    </div>

                    <!-- Block 3: Tài liệu độc quyền -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-right" data-aos-duration="800" data-aos-delay="800">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/book-reader.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Tài liệu độc quyền – Học thực tế, dễ hiểu, sát đề thi
                            </h2>
                        </div>
                        <div
                            class="reason-content text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <p class="!mb-3">
                                Sử dụng giáo trình do Dahee tự biên soạn, đã được kiểm
                                nghiệm qua hàng ngàn học viên
                            </p>
                            <p class="!mb-3">Học viên được cấp:</p>
                            <ul class="list-disc list-outside pl-10 space-y-2">
                                <li>File ngữ pháp, từ vựng, bài tập dịch Hàn – Việt</li>
                                <li>Đề thi TOPIK miễn phí</li>
                                <li>Video bài giảng, tài liệu tổng hợp thực chiến</li>
                                <li>Ghi lại toàn bộ bài học để xem lại bất cứ lúc nào</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div
                    class="!border-0 !border-l border-dashed border-[#3B92FF] hidden lg:block"
                ></div>
                <!-- Right Column -->
                <div class="w-full lg:w-1/2 flex-auto space-y-6">
                    <!-- Block 4: Tiết kiệm chi phí -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-left" data-aos-duration="800" data-aos-delay="400">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/piggy-bank.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Tiết kiệm chi phí – Ưu đãi trọn gói theo lộ trình
                            </h2>
                        </div>
                        <ul
                            class="reason-list list-disc list-outside pl-4 text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <li>
                                Học combo theo lộ trình giúp tiết kiệm đến 30% chi phí so
                                với đăng ký từng khóa lẻ.
                            </li>
                            <li>
                                Tặng kèm nhiều khóa bổ trợ (ôn thi TOPIK, từ vựng Hán
                                Hàn...)
                            </li>
                            <li>
                                Không phát sinh thêm chi phí, đăng ký 1 lần là học đến khi
                                đạt mục tiêu.
                            </li>
                        </ul>
                    </div>

                    <!-- Block 5: Cam kết đầu ra -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-left" data-aos-duration="800" data-aos-delay="600">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/user-graduate-white.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Cam kết đầu ra rõ ràng – Học lại miễn phí nếu chưa đạt
                            </h2>
                        </div>
                        <ul
                            class="reason-list list-disc list-outside pl-4 text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <li>
                                Mỗi khóa học đều đi kèm bài test giữa & cuối khóa theo 4 kỹ
                                năng.
                            </li>
                            <li>
                                Nếu thi chưa đạt đầu ra, được học lại và ôn thi lại hoàn
                                toàn miễn phí.
                            </li>
                            <li>
                                Học viên có thể bảo lưu tối đa 6 tháng, linh hoạt lịch học.
                            </li>
                        </ul>
                    </div>

                    <!-- Block 6: Hỗ trợ sát sao -->
                    <div class="reason-card p-0 lg:!p-4" data-x-aos="slide-left" data-aos-duration="800" data-aos-delay="800">
                        <div
                            class="p-1 gap-2 !pr-3 flex items-center rounded-full bg-[#3B92FF] xl:h-15 xl:gap-[15px] xl:px-2 !mb-4 xl:!mb-5"
                        >
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/users.svg') }}"
                                alt=""
                                width="46px"
                                height="46px"
                                class="xl:w-[46px] xl:h-[46px] object-cover"
                            />
                            <h2
                                class="reason-title font-semibold text-white text-lg leading-5"
                            >
                                Hỗ trợ sát sao – Không bao giờ bị bỏ rơi
                            </h2>
                        </div>
                        <ul
                            class="reason-list list-disc list-outside pl-4 text-[#313131] text-base leading-[1.3] text-justify"
                        >
                            <li>Được theo dõi tiến trình cá nhân, điều chỉnh nếu cần.</li>
                            <li>
                                Có nhóm học viên riêng tập cộng – giao tiếp thực tế mỗi
                                tuần.
                            </li>
                            <li>
                                Đội ngũ hỗ trợ giải đáp nhanh chóng, đồng hành suốt hành
                                trình học.
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="teachers" class="teachers pt-8 pb-16 md:py-12 xl:pt-16 xl:pb-15">
        <div class="container mx-auto !px-4 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <!-- Main Title -->
            <div class="text-center !mb-10 md:!mb-14 lg:!mb-16" data-x-aos="fade-down" data-aos-duration="800">
                <h2
                    class="teachers-title combo-highlight xl:mb-2.5 after:w-28 after:right-1/2 after:translate-x-1/2 xl:after:translate-x-0 xl:after:right-0 xl:after:w-[159px] after:-bottom-2 text-[32px] lg:text-4xl xl:text-[52px] xl:leading-[1.1] gradient-text font-semibold w-fit mx-auto"
                >
                    Đội ngũ giáo viên giỏi tại Dahee
                </h2>
            </div>

            <!-- Teacher Introduction Section -->
            <div
                class="bg-[#0848B7] xl:rounded-[30px] rounded-[20px] !pt-8 !px-4 xl:!px-6 xl:!py-15 xl:gap-x-[420px] text-white xl:text-base xl:leading-[22px] font-normal flex flex-col lg:flex-row lg:flex-wrap xl:flex-nowrap items-center justify-between xl:mb-3 relative"
                data-x-aos="zoom-in" data-aos-duration="1000" data-aos-delay="200"
            >
                <!-- Left Content -->
                <div class="space-y-3 text-base relative z-10 flex-1">
                    <div class="flex gap-2 mb-4">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/star.svg') }}"
                            alt=""
                            width="36"
                            height="36"
                            class="size-6 xl:w-9 xl:h-9 object-cover"
                        />
                        <span class="[font:inherit]"
                            >100% đạt TOPIK 5–6, có bằng Cử nhân và Thạc sĩ chuyên ngành
                            Ngôn ngữ Hàn Quốc, tốt nghiệp tại Hàn Quốc</span
                        >
                    </div>
                    <div class="flex gap-2 mb-4">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/star.svg') }}"
                            alt=""
                            width="36"
                            height="36"
                            class="size-6 xl:w-9 xl:h-9 object-cover"
                        />
                        <span class="[font:inherit]"
                            >Kinh nghiệm giảng dạy lâu năm, từng là phiên dịch viên chuyên
                            nghiệp, phát âm chuẩn giọng Seoul</span
                        >
                    </div>
                    <div class="flex gap-2 mb-4">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/star.svg') }}"
                            alt=""
                            width="36"
                            height="36"
                            class="size-6 xl:w-9 xl:h-9 object-cover"
                        />
                        <span class="[font:inherit]"
                            >Luôn hỗ trợ học viên cả trong và ngoài giờ học, không để bạn
                            "tự bơi" trên hành trình học tiếng</span
                        >
                    </div>
                </div>

                <!-- Center Image -->
                <div class="hidden xl:block absolute bottom-0 left-1/2 -translate-x-1/2">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/giao vien.png') }}"
                        alt="Teacher Image"
                        width="412px"
                        height="419px"
                        class="teachers-main-image w-40 md:w-56 xl:w-[412px] relative z-10"
                    />
                </div>

                <!-- Right Content -->
                <div class="space-y-3 text-base relative z-10 flex-1">
                    <div class="flex gap-2 mb-2.5">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/star.svg') }}"
                            alt=""
                            width="36"
                            height="36"
                            class="size-6 xl:w-9 xl:h-9 object-cover"
                        />
                        <span class="[font:inherit] !font-semibold"
                            >1 lớp = 3 giáo viên đồng hành:</span
                        >
                    </div>
                    <ul
                        class="[font:inherit] list-disc list-outside space-y-2 ml-12 lg:ml-16"
                    >
                        <li>Giảng viên dưỡng lớp</li>
                        <li>Giáo viên chữa bài viết, luyện nói</li>
                        <li>Giáo viên tổng ôn và hỗ trợ luyện đề</li>
                    </ul>
                    <div class="flex gap-2 mt-4">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/star.svg') }}"
                            alt=""
                            width="36"
                            height="36"
                            class="size-6 xl:w-9 xl:h-9 object-cover"
                        />
                        <span class="[font:inherit]"
                            >Sĩ số lớp nhỏ (Chưa tới 10 học viên mỗi lớp), giúp theo sát
                            từng học viên, điều chỉnh tốc độ và nội dung học phù hợp.</span
                        >
                    </div>
                </div>

                <!-- Center Image -->
                <div class="mx-auto w-full xl:hidden">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/giao vien.png') }}"
                        alt="Teacher Image"
                        width="412px"
                        height="419px"
                        class="teachers-main-image w-[334px] xl:w-[412px] relative z-10 mx-auto !mt-5"
                    />
                </div>
            </div>

            <!-- Teacher Cards Carousel -->
            <div
                class="teachers-carousel-container w-full [&_.slick-list]:w-full [&_.slick-list]:overflow-y-visible relative"
                data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="400"
            >
                <!-- Cards Container -->
                <div class="teachers-cards-wrapper">
                    <!-- Navigation Arrows -->
                    <button
                        class="teachers-nav-btn teachers-nav-prev absolute left-0 lg:-left-5 top-1/2 transform -translate-y-1/2 z-10"
                    >
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/arrow-left.svg') }}"
                            width="10"
                            height="21"
                            alt=""
                            class="h-5 w-2.5 object-cover"
                        />
                    </button>
                    <button
                        class="teachers-nav-btn teachers-nav-next absolute right-0 lg:-right-5 top-1/2 transform -translate-y-1/2 z-10"
                    >
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/arrow-right.svg') }}"
                            width="10"
                            height="21"
                            alt=""
                            class="h-5 w-2.5 object-cover"
                        />
                    </button>
                    <div
                        class="teachers-cards-container [&.slick-dotted.slick-slider]:mb-1 [&.slick-dotted.slick-slider]:lg:mb-7 flex transition-transform duration-300 ease-in-out space-x-6 py-2 lg:py-6"
                    >
                        <!-- Teacher Card 1: Cô Lan Nhí -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-1.jpg') }}"
                                        alt="Cô Lan Nhí"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Lan Nhi
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>Khoa ngôn ngữ Hàn Quốc – Đại học Hà Nội</li>
                                            <li>
                                                Cử nhân khoa Hợp tác Phát triển Quốc tế –
                                                Đại học Quốc gia Pukyong, Busan, Hàn Quốc
                                                (điểm tốt nghiệp 93/100)
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                6 năm giảng dạy tại học viện ngoại ngữ của
                                                Busan
                                            </li>
                                            <li>
                                                Hoạt động page dạy tiếng Hàn (đào tạo hơn
                                                1000 học viên với hơn 100 lớp tiếng Hàn từ
                                                số 0 đến topik 6)
                                            </li>
                                            <li>
                                                Từng làm phiên dịch dự án: Samsung, XKLD tại
                                                Busan, Phòng đối ngoại của các trung tâm du
                                                học, IT,...
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 2: Cô Hà Thu -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-2.png') }}"
                                        alt="Cô Hà Thu"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Hà Thu
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Cử nhân khoa Ngôn ngữ Hàn Quốc Trường Đại
                                                học Ngoại ngữ, Đại học Quốc gia Hà Nội
                                            </li>
                                            <li>
                                                Thạc sĩ ngành Biên dịch Hàn - Việt, trường
                                                Đại học Ngoại ngữ Busan Hàn Quốc
                                            </li>
                                            <li>
                                                Cử nhân ngành Ngôn ngữ học, trường Đại học
                                                Khoa học Xã hội và Nhân văn Hà Nội
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Biên dịch sách tiếng Hàn tại cty Bizbook
                                                Joint Stock Company, Cty Tân Việt Books, ...
                                            </li>
                                            <li>
                                                Phiên dịch viên: Cty Samsung Electro -
                                                Mechanics Việt Nam, ...
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 3: Cô Tú Anh -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-3.png') }}"
                                        alt="Cô Tú Anh"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Tú Anh
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>Khoa ngôn ngữ Hàn Quốc - Đại học Hà Nội</li>
                                            <li>
                                                Du học tại trường Đại học Deagu, Hàn Quốc
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Nhiều năm giảng dạy tại trung tâm tiếng Hàn,
                                                trung tâm du học, từ sơ cấp đến ôn thi topik
                                                5,6
                                            </li>
                                            <li>
                                                Thông dịch viên: Ký kết hợp tác giữa các
                                                trường đại học VN-HQ, phiên dịch cho các
                                                công ty Hàn Quốc tại Việt Nam, ...
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 4: Cô Minh Ngọc -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-4.png') }}"
                                        alt="Cô Minh Ngọc"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Minh Ngọc
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Cử nhân khoa Hàn Quốc học của trường Đại Học
                                                Ngoại Ngữ - Đại học Quốc Gia Hà Nội
                                            </li>
                                            <li>
                                                Học thạc sĩ ngành Tâm lý học - Trường Đại
                                                học Gachon,Gyeonggi-do, Hàn Quốc
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Nhiều năm giảng dạy tại: Trung tâm Giáo Dục
                                                Visang; Học viện ngôn ngữ Hàn Quốc Đông Á,
                                                Trường Đại học Kinh doanh và Công nghệ Hà
                                                Nội,...
                                            </li>
                                            <li>
                                                Thông dịch viên: Trung tâm Sunny, Đại sứ
                                                quán Hàn Quốc,...
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 5: Cô Huyền Thương -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-5.png') }}"
                                        alt="Cô Huyền Thương"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Huyền Thương
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Đại học Ngoại ngữ - Đại học Quốc gia Hà Nội
                                            </li>
                                            <li>
                                                Du học tại trường Đại học Konkuk, Seoul, Hàn
                                                Quốc
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Nhiều năm giảng dạy tiếng Hàn: Dạy tiếng Hàn
                                                cho người Việt kiêm tiếng Việt cho người Hàn
                                            </li>
                                            <li>
                                                Phiên dịch viên: Cty Samsung Electrongnics
                                                Viet Nam, Hiệp hội doanh nghiệp Hàn Quốc tại
                                                Việt Nam, Thư ký kiêm phiên dịch cho tổng
                                                giám đốc trong các cuộc họp với Bí thư
                                                Tỉnh/TP
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 6: Cô Hạ Nam -->
                        <div class="pt-22 relative w-full xl:w-[365px] px-3">
                            <div
                                class="teachers-card flex-shrink-0 bg-white rounded-2xl shadow-md border border-gray-200 !p-4 hover:shadow-lg transition-shadow"
                            >
                                <div class="flex items-center gap-3 mb-16">
                                    <img
                                        src="{{ asset('assets/frontend/dahee/assets/images/teacher-6.png') }}"
                                        alt="Cô Hạ Nam"
                                        class="teachers-avatar absolute left-0 -top-11 lg:-top-15 w-32 h-32 lg:w-[180px] lg:h-[180px] rounded-full object-cover"
                                    />
                                    <div class="ml-[120px] lg:ml-[175px]">
                                        <p
                                            class="teachers-name font-semibold text-2xl text-[#313131]"
                                        >
                                            Cô Hạ Nam
                                        </p>
                                        <div
                                            class="teachers-badge flex items-center justify-center gap-2 text-white text-lg font-normal bg-gradient-to-r from-[#406BF2] to-[#003EB0] !px-2 !py-1 rounded-full"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/quality.svg') }}"
                                                alt=""
                                                width="26"
                                                height="26"
                                                class="w-[26px] h-[26px]"
                                            />
                                            <span>TOPIK 6</span>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="space-y-1 xl:text-[15px] xl:leading-5 bg-gradient-to-b from-[#DCEBFF] to-white xl:rounded-[20px] !py-2 !px-1 text-[#313131]"
                                >
                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> HỌC VẤN: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Khoa tiếng Hàn Trường Đại học Khoa học Xã
                                                hội và Nhân văn TP HCM
                                            </li>
                                            <li>
                                                Thạc sĩ khoa Ngữ Văn Hàn Quốc Trường Đại học
                                                Quốc Gia Seoul, Hàn Quốc
                                            </li>
                                        </ul>
                                    </div>

                                    <div>
                                        <p
                                            class="teachers-section-title xl:text-lg xl:leading-[34px] font-semibold flex items-center gap-1 text-[#003EB0] !mb-1"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dahee/assets/images/star-2.svg') }}"
                                                alt=""
                                                width="24"
                                                height="24"
                                            />
                                            <span> KINH NGHIỆM: </span>
                                        </p>
                                        <ul
                                            class="list-disc list-outside pl-4 space-y-1 ml-2"
                                        >
                                            <li>
                                                Nhiều năm giảng dạy tiếng Hàn: Trung tâm du
                                                học, trung tâm tiếng Hàn lớn ở Việt Nam và
                                                Hàn Quốc
                                            </li>
                                            <li>
                                                Thông dịch viên: Tại các cty thương mại Hàn
                                                Quốc: Youngnam, Deayoung, Jamun Vina,...
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carousel Dots -->
            <div class="teachers-dots mt-6"></div>
        </div>
    </section>

    <section
        id="feedback"
        class="bg-gradient-to-b from-[#436AE5] to-[#003EB0] py-7 lg:py-20"
    >
        <div class="container mx-auto !px-4 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <!-- Section Header -->
            <div class="text-center max-w-4xl mx-auto mb-7 lg:mb-16" data-x-aos="fade-down" data-aos-duration="800">
                <h2
                    class="text-[32px] relative combo-highlight md:text-5xl after:w-[108px] after:h-2 after:-bottom-2 after:right-1/2 after:translate-x-1/2 font-extrabold text-white mb-4 txt-gra-hv"
                >
                    Học viên nói gì về Tiếng Hàn Dahee?
                </h2>
                <p class="text-xl text-blue-100">
                    Hàng ngàn học viên đã và đang chinh phục thành công tiếng Hàn cùng Dahee
                    – từ những người bắt đầu từ con số 0 đến các bạn thi đỗ TOPIK 4, 5, 6,
                    đạt học bổng hoặc làm việc tại công ty Hàn Quốc.
                </p>
            </div>

            <!-- Swiper Container -->
            <div class="swiper-container" data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="swiper-wrapper pb-10 feedback-swiper !mb-10 lg:!mb-15">
                    <!-- Slide 1: Khánh Chi -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                2 cô giáo dạy là cô Lan Nhi và cô Hà Thu đều dạy rất tận
                                tâm, dạy siêu hay và dễ hiểu ý ạ. Lên lớp với các cô học
                                thoải mái không cảm thấy bị áp lực chuyện học một tí gì lun
                                ạ( làm e yêu tiếng Hàn thêm lunn). Chương trình học cô cho
                                nhiều bài tập để va chạm với kiến thức rất hiệu quả luôn ạ.
                                Sau khi học xong lớp sc1 thì em vẫn muốn tiếp tục đki các
                                lớp tiếp theo vì Tiếng Hàn Dahee quá tuỵt vời ạ 🫶🏻
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-1.png') }}"
                                alt="Ảnh đại diện của Khánh Chi"
                            />
                            <h4 class="text-white text-xl">Khánh Chi</h4>
                        </div>
                    </div>

                    <!-- Slide 2: Minh Trang -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                Ngay từ buổi đầu học, bất ngờ bật ngửa ngơ ngác, ấn tượng
                                cực kỳ. Lộ trình được vạch rõ ngay từ buổi đầu đánh bay mọi
                                mơ hồ. Khi niềm tin được xây dựng, cô đã củng cố niềm tin
                                của em bằng cách dạy thực tế và đánh đúng trọng tâm. Cực kỳ
                                ấn tượng câu nói của cô "Nên học để thi lấy bằng hay học để
                                dùng được tiếng? Sao phải chọn khi mình làm được cả hai
                                nhỉ?" Cách ghi chép hợp lý, không lý thuyết dài dòng. Ví dụ
                                sinh động sau mỗi ngữ pháp được học, kết hợp với việc chia
                                sẻ văn hóa Hàn Quốc khiến bài học ×10 sự cuốn
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-2.png') }}"
                                alt="Ảnh đại diện của Hồng Mây"
                            />
                            <h4 class="text-white text-xl">Minh Trang</h4>
                        </div>
                    </div>

                    <!-- Slide 3: Hồng Mây -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                1000000000đ không có nhưng ạ 💯🫶 Em chỉ vô tình biết đến lớp
                                thôi nhưng mà sau buổi học đầu tiên của lớp SC1 là em quyết
                                định chốt sale luôn, học đến khi nào em topik 6 ạ🫰 Các cô
                                dạy siêu kĩ, giọng nhẹ nhàng cute mà thấm lắm ạ.Em thấy học
                                với cô, em vẫn cân được cả lý thuyết với giao tiếp nói được
                                ý, có nền tảng chắc nên làm gì cũng dễ dàng hơn.
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-3.png') }}"
                                alt="Ảnh đại diện của Kim Anh"
                            />
                            <h4 class="text-white text-xl">Hồng Mây</h4>
                        </div>
                    </div>
                    <!-- Slide 1: Phương Anh -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                Cô dạy dễ vào xong còn có mẹo học 👍🏻 Cô còn nắm được sẽ có
                                những câu hỏi gì, về vấn đề gì xong trấn an tinh thần em nữa
                                🤣 từ mới đầu đã có cô chỉnh phát âm cho và cô cũng tạo động
                                lực siêu nhiều hehee. Em chỉ việc yên tâm học bài, chữa bài
                                đã có cô lo. Không phải bỗng dưng mới học SC1 mà em hỏi cô
                                có dạy TC không, tại em muốn theo cô học cao hơn. Gói gọn
                                lại trong 2 chữ yên tâm
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-1.png') }}"
                                alt="Ảnh đại diện của Khánh Chi"
                            />
                            <h4 class="text-white text-xl">Phương Anh</h4>
                        </div>
                    </div>

                    <!-- Slide 2: Dương -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                Trước chưa học em cứ nghĩ page ""Tiếng Hàn dahee"" kiểu flex
                                hơi quá ý Nma học rồi mới thấy hoá ra đỉnh thật. Đỉnh từ
                                giáo viên đến bài tập lun ý ạ. Giờ mà rời xa cô Lan Nhi chắc
                                em ra đường không biết đếm tiền Hàn luôn quá🤣Mê cái cách cô
                                giáo iem có thể thâu tóm lại bài học và vận dụng trong 10p
                                cuối giờ. Cùng kiến thức nma cô dạy kết hợp với kiến thức xã
                                hội nên dễ tiếp thu dễ nhớ hơn.Và khẳng định là: Học xong là
                                nhớ ngữ pháp ngayyyy.Siêu yêu Tiếng Hàn dahee
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-2.png') }}"
                                alt="Ảnh đại diện của Hồng Mây"
                            />
                            <h4 class="text-white text-xl">Dương</h4>
                        </div>
                    </div>

                    <!-- Slide 3: Kim Anh -->
                    <div class="swiper-slide flex flex-col items-center px-2">
                        <div
                            class="bg-white rounded-xl p-8 shadow-lg relative testimonial-card w-full max-w-md mx-auto lg:min-h-[320px]"
                        >
                            <p class="text-[#313131] leading-relaxed">
                                Em cũng từng học tiếng Hàn ở nhiều nơi nhưng cách cô dạy là
                                một cái gì đó rất khác so với thị trường. Cô giảng siêu dễ
                                hiểu, không máy móc hay sách vở mà giọng cô còn hay xỉu luôn
                                á. Khoản chấm chữa bài siêu chi tiết luôn ạ. Em siêu kết kho
                                bài tập tập độ sộ của cô á học cái áp dụng làm luôn nhớ bài
                                luôn. Học cô mà tâm hồn em cứ rạo rực chứ muốn bay ngay sang
                                Hàn thôi thích Hàn quá rồi cô ạ 🥰🥰🥰
                            </p>
                        </div>
                        <div class="flex items-center mt-8 gap-2 justify-center">
                            <img
                                class="w-[58px] h-[58px] rounded-full object-cover"
                                src="{{ asset('assets/frontend/dahee/assets/images/fb-avatar-3.png') }}"
                                alt="Ảnh đại diện của Kim Anh"
                            />
                            <h4 class="text-white text-xl">Kim Anh</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container mx-auto !px-4 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <!-- Tiêu đề chính -->
            <div class="flex justify-center">
                <h1
                class="text-[32px] md:text-5xl leading-[1.2] text-center !mb-5 lg:!mb-12 lg:text-5xl font-semibold txt-gra-hv combo-highlight"
                data-x-aos="fade-down" data-aos-duration="800"
            >
                Hình ảnh Feedback
            </h1>
            </div>
            <div class="columns-3 lg:columns-5 gap-1.5 space-y-1.5 lg:space-y-4" data-x-aos="zoom-in" data-aos-duration="1000" data-aos-delay="200">
                <!-- Ghi chú: Sử dụng ảnh placeholder với các kích thước khác nhau -->
                <!-- Thuộc tính 'break-inside-avoid' ngăn ảnh bị ngắt giữa các cột -->

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb1.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb2.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb3.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb4.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb5.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb6.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb7.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb8.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb9.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb10.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb11.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>

                <div class="break-inside-avoid">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/fb12.png') }}"
                        alt=""
                        class="w-full h-auto rounded-md lg:rounded-xl shadow-lg transition-transform duration-300 hover:scale-105"
                    />
                </div>
            </div>

            <!-- Nút Xem Thêm -->
            <div class="text-center text-lg font-medium mt-6">
                <button
                    id="feedback-btn"
                    class="bg-[linear-gradient(270deg,_#BFF2FF_0%,_#FFF_47.6%,_#FFFBAB_100%)] py-2 px-10 rounded-full hover:bg-yellow-400 transition-all duration-300 shadow-lg hover:shadow-yellow-400/50 transform hover:-translate-y-1 text-[#0057E4]"
                >
                    Xem thêm Feedback
                </button>
            </div>
        </div>
    </section>

    <section
        id="commitment"
        class="py-6 xl:py-15"
        style="
            background-image: url('{{ asset('assets/frontend/dahee/assets/images/bg-ck.png') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        "
    >
        <div class="container mx-auto !px-4 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <div class="max-w-7xl mx-auto text-center">
                <h2
                    class="relative text-center combo-highlight after:w-[108px] after:h-2 after:-bottom-2 after:right-1/2 after:translate-x-1/2 text-3xl md:text-4xl font-bold gradient-text mb-2 block"
                    data-x-aos="fade-down" data-aos-duration="800"
                >
                    Cam kết tại <br />
                    Tiếng Hàn Dahee
                </h2>

                <div class="flex md:hidden justify-center items-center !mt-4" data-x-aos="zoom-in" data-aos-duration="800" data-aos-delay="200">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/user-commit.png') }}"
                        alt="Student learning"
                        class="w-full h-auto object-cover"
                    />
                </div>

                <div
                    class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-4 lg:gap-6 lg:mt-15"
                    data-x-aos="fade-up" data-aos-duration="800" data-aos-delay="400"
                >
                    <div class="flex flex-col gap-12">
                        <!-- Commitment 1 -->
                        <div
                            class="bg-[#DAEAFF] py-6 !px-5 text-[#313131] rounded-[30px] shadow-md flex flex-col items-start relative"
                        >
                            <div
                                class="absolute -top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 w-[70px] h-[70px] rounded-full bg-[#0057E4] text-white flex items-center justify-center font-bold text-[36px] border-[7px] border-white"
                            >
                                1
                            </div>
                            <h3
                                class="font-semibold text-[22px] leading-6 mt-6 mb-2 text-center w-full"
                            >
                                Đầu ra rõ ràng, có kiểm tra <br />
                                đánh giá cụ thể
                            </h3>
                            <ul
                                class="list-disc list-outside !pl-4 space-y-2 text-justify text-base"
                            >
                                <li>
                                    Mỗi khóa học đều có test giữa và cuối khóa theo đủ 4 kỹ
                                    năng: Nghe – Nói – Đọc – Viết
                                </li>
                                <li>
                                    Kết quả được theo dõi sát sao để điều chỉnh lộ trình học
                                    phù hợp từng học viên
                                </li>
                            </ul>
                        </div>

                        <!-- Commitment 2 -->
                        <div
                            class="bg-[#FFEFC8] py-6 !px-5 text-[#313131] rounded-[30px] shadow-md flex flex-col items-start relative"
                        >
                            <div
                                class="absolute -top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 w-[70px] h-[70px] rounded-full bg-[#0057E4] text-white flex items-center justify-center font-bold text-[36px] border-[7px] border-white"
                            >
                                2
                            </div>
                            <h3
                                class="font-semibold text-[22px] leading-6 mt-6 mb-2 text-center w-full"
                            >
                                Học lại miễn phí nếu chưa đạt <br />
                                mục tiêu
                            </h3>
                            <ul
                                class="list-disc list-outside !pl-4 space-y-2 text-justify text-base"
                            >
                                <li>
                                    Nếu học viên chưa đạt yêu cầu đầu ra hoặc thi trượt
                                    TOPIK ở lần đầu, được học lại miễn phí toàn bộ khóa ôn
                                </li>
                                <li>Không thu thêm bất kỳ chi phí phát sinh nào</li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-col gap-12">
                        <!-- Center Image (hidden on small) -->
                        <div class="hidden md:flex justify-center items-center">
                            <img
                                src="{{ asset('assets/frontend/dahee/assets/images/user-commit.png') }}"
                                alt="Student learning"
                                class="object-cover"
                            />
                        </div>

                        <!-- Commitment 3 -->
                        <div
                            class="bg-[#DAEAFF] py-6 !px-5 text-[#313131] rounded-[30px] shadow-md flex flex-col items-start relative"
                        >
                            <div
                                class="absolute -top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 w-[70px] h-[70px] rounded-full bg-[#0057E4] text-white flex items-center justify-center font-bold text-[36px] border-5 border-white"
                            >
                                3
                            </div>
                            <h3
                                class="font-semibold text-[22px] leading-6 mt-6 mb-2 text-center w-full"
                            >
                                Tài liệu học tập rõ ràng – Bài <br />
                                học có thể xem lại
                            </h3>
                            <ul
                                class="list-disc list-outside !pl-4 space-y-2 text-justify text-base"
                            >
                                <li>
                                    Học viên được cấp toàn bộ tài liệu độc quyền (Được biên
                                    soạn riêng bởi nhà Dahee - có xuất bản sách và bán)
                                </li>
                                <li>
                                    Toàn bộ buổi học được ghi lại, xem lại bất kỳ lúc nào
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-col gap-12">

                        <!-- Commitment 5 -->
                        <div
                            class="bg-[#DAEAFF] py-6 !px-5 text-[#313131] rounded-[30px] shadow-md flex flex-col items-start relative"
                        >
                            <div
                                class="absolute -top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 w-[70px] h-[70px] rounded-full bg-[#0057E4] text-white flex items-center justify-center font-bold text-[36px] border-5 border-white"
                            >
                                5
                            </div>
                            <h3
                                class="font-semibold text-[22px] leading-6 mt-6 mb-2 text-center w-full"
                            >
                                Học viên được hỗ trợ sát sao <br />
                                đến khi đạt kết quả
                            </h3>
                            <ul
                                class="list-disc list-outside !pl-4 space-y-2 text-justify text-base"
                            >
                                <li>Có đội ngũ hỗ trợ học tập 1:1 suốt 24h</li>
                                <li>Cập nhật tiến trình học – đánh giá định kỳ</li>
                                <li>
                                    Học viên không học một mình mà luôn có người đồng hành
                                </li>
                            </ul>
                        </div>

                        <!-- Commitment 4 -->
                        <div
                            class="bg-[#FFEFC8] py-6 !px-5 text-[#313131] rounded-[30px] shadow-md flex flex-col items-start relative"
                        >
                            <div
                                class="absolute -top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 w-[70px] h-[70px] rounded-full bg-[#0057E4] text-white flex items-center justify-center font-bold text-[36px] border-5 border-white"
                            >
                                4
                            </div>
                            <h3
                                class="font-semibold text-[22px] leading-6 mt-6 mb-2 text-center w-full"
                            >
                                Lịch học linh hoạt – Được bảo <br />
                                lưu đến 6 tháng
                            </h3>
                            <ul
                                class="list-disc list-outside !pl-4 space-y-2 text-justify text-base"
                            >
                                <li>Phù hợp với người đi làm, sinh viên bận rộn</li>
                                <li>Có thể chọn khung giờ tối 2/4/6 hoặc 3/5/7</li>
                                <li>Được bảo lưu khóa học tối đa 6 tháng không mất phí</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section
        id="form-register"
        class="py-6 md:py-8 lg:py-15"
        style="
            background-image: url('{{ asset('assets/frontend/dahee/assets/images/bg-form.png') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        "
    >
        <div class="container mx-auto !px-4 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <div class="flex flex-col lg:flex-row gap-10">
                <div class="w-full lg:w-1/2 flex flex-col items-center justify-center" data-x-aos="fade-right" data-aos-duration="800">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/ko-flag.png') }}"
                        alt="Form Image"
                        class="size-[57px] xl:size-[75px] mb-1"
                    />
                    <div
                        class="text-center text-[52px] leading-[1.1] font-semibold text-[#57A2FF] mt-2"
                    >
                        Tiếng Hàn<br />không khó
                    </div>
                    <div
                        class="w-[273px] h-[42px] md:h-auto py-2 px-5 bg-gradient-to-r from-[#406BF2] to-[#003EB0] rounded-full mt-3"
                    >
                        <div
                            class="text-white text-center text-[30px] leading-[1.1] font-semibold"
                        >
                            Vì có Dahee
                        </div>
                    </div>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="227"
                        height="60"
                        viewBox="0 0 227 60"
                        fill="none"
                        class="hidden lg:block translate-x-[200px] mt-6"
                    >
                        <path
                            d="M226.55 11.7025L220.938 28.0887L209.553 15.0355L226.55 11.7025ZM1.89001 1.5707L3.03799 0.605226C3.8069 1.5195 4.57769 2.42045 5.35027 3.30821L4.21875 4.29293L3.08723 5.27764C2.30349 4.37705 1.52172 3.46326 0.742016 2.53616L1.89001 1.5707ZM8.90999 9.50094L10.0068 8.47774C11.6302 10.218 13.2608 11.9023 14.8979 13.5319L13.8397 14.595L12.7814 15.6581C11.1177 14.0019 9.46143 12.291 7.81316 10.5241L8.90999 9.50094ZM18.9114 19.4532L19.9276 18.3499C21.6627 19.948 23.4043 21.4871 25.1516 22.9687L24.1814 24.1128L23.2113 25.2568C21.4324 23.7484 19.6601 22.1821 17.8952 20.5565L18.9114 19.4532ZM29.6732 28.5698L30.5927 27.3847C32.4537 28.8285 34.3202 30.2098 36.1914 31.5301L35.3266 32.7557L34.4618 33.9813C32.5532 32.6346 30.6502 31.2263 28.7537 29.7549L29.6732 28.5698ZM41.1489 36.6626L41.9548 35.3975C43.9495 36.6681 45.9484 37.8723 47.9506 39.0121L47.2085 40.3157L46.4664 41.6193C44.4208 40.4547 42.3793 39.2249 40.343 37.9277L41.1489 36.6626ZM53.4248 43.6476L54.0991 42.3078C56.1959 43.3631 58.2953 44.3506 60.3959 45.2726L59.793 46.6461L59.1901 48.0196C57.0412 47.0764 54.8942 46.0665 52.7504 44.9875L53.4248 43.6476ZM66.3055 49.299L66.8337 47.8951C69.0271 48.7203 71.2207 49.477 73.4131 50.1677L72.9624 51.5984L72.5116 53.029C70.2673 52.322 68.0221 51.5475 65.7773 50.7029L66.3055 49.299ZM79.7482 53.5324L80.1194 52.0791C82.3875 52.6583 84.6529 53.1697 86.9143 53.6162L86.6237 55.0878L86.3331 56.5594C84.0182 56.1023 81.699 55.5788 79.3771 54.9858L79.7482 53.5324ZM93.5706 56.2627L93.7802 54.7774C96.0928 55.1039 98.3995 55.365 100.699 55.5639L100.569 57.0583L100.44 58.5527C98.0879 58.3492 95.7276 58.082 93.3609 57.7479L93.5706 56.2627ZM107.6 57.4792L107.65 55.98C109.982 56.0581 112.305 56.0746 114.616 56.0329L114.644 57.5326L114.671 59.0324C112.309 59.075 109.935 59.0582 107.55 58.9783L107.6 57.4792ZM121.681 57.2286L121.579 55.7321C123.908 55.5734 126.224 55.3578 128.524 55.0889L128.699 56.5787L128.873 58.0686C126.525 58.343 124.161 58.5631 121.783 58.7251L121.681 57.2286ZM135.679 55.5963L135.436 54.1161C137.741 53.737 140.029 53.3064 142.297 52.828L142.607 54.2958L142.916 55.7635C140.606 56.2508 138.274 56.6898 135.923 57.0764L135.679 55.5963ZM149.471 52.6919L149.098 51.2389C151.365 50.6577 153.609 50.031 155.828 49.3627L156.26 50.799L156.693 52.2353C154.435 52.9152 152.151 53.5531 149.843 54.1449L149.471 52.6919ZM162.967 48.6311L162.478 47.2132C164.694 46.4478 166.882 45.6431 169.04 44.8034L169.584 46.2012L170.128 47.5991C167.935 48.4525 165.711 49.2706 163.457 50.0489L162.967 48.6311ZM176.104 43.5215L175.508 42.145C177.665 41.2107 179.788 40.2437 181.874 39.2488L182.52 40.6027L183.166 41.9565C181.048 42.9668 178.892 43.9489 176.7 44.898L176.104 43.5215ZM188.827 37.454L188.133 36.1241C190.225 35.0324 192.274 33.9155 194.277 32.7788L195.017 34.0833L195.758 35.3878C193.725 36.5417 191.645 37.6755 189.52 38.7839L188.827 37.454ZM201.085 30.4964L200.298 29.2189C202.318 27.9763 204.282 26.7176 206.187 25.4493L207.018 26.6979L207.85 27.9465C205.915 29.2345 203.921 30.5125 201.871 31.774L201.085 30.4964ZM212.805 22.6905L211.929 21.4734C213.867 20.0775 215.731 18.6781 217.516 17.2834L218.439 18.4655L219.363 19.6476C217.546 21.0664 215.651 22.4892 213.682 23.9076L212.805 22.6905Z"
                            fill="#FFAA39"
                        />
                    </svg>
                </div>
                <div class="w-full lg:w-1/2" data-x-aos="fade-left" data-aos-duration="800" data-aos-delay="200">
                    <img src="{{ asset('assets/frontend/dahee/assets/images/book-form.png') }}" alt="" class="w-full h-auto" />
                </div>
            </div>

            <div
                class="text-white rounded-[30px] py-6 !px-4 xl:!p-6 flex flex-col lg:flex-row gap-10 items-center"
                style="
                    background-image: url('{{ asset('assets/frontend/dahee/assets/images/bg-form-child.png') }}');
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                "
                data-x-aos="zoom-in"
                data-aos-duration="1000"
                data-aos-delay="400"
            >
                <div class="w-full lg:w-1/4 flex justify-center flex-col items-center">
                    <h3
                        class="text-[32px] relative font-bold mb-4 combo-highlight after:w-[108px] after:h-2 after:-bottom-2 after:right-1/2 after:translate-x-1/2 txt-gra-hv text-center lg:text-[40px] lg:leading-[42px]"
                    >
                        Đăng ký nhận<br />Tư vấn từ Dahee
                    </h3>
                </div>

                <form id="user-inquiry-form" class="space-y-4 flex-1 w-full">
                    <div id="form-message" class="hidden p-3 rounded-lg text-center font-medium"></div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input
                            type="text"
                            name="name"
                            placeholder="Nhập họ và tên *"
                            class="rounded-full px-4 py-2 w-full text-black"
                            required
                        />
                        <input
                            type="tel"
                            name="phone"
                            placeholder="Nhập số điện thoại *"
                            class="rounded-full px-4 py-2 w-full text-black"
                            required
                        />
                        <input
                            type="email"
                            name="email"
                            placeholder="Nhập email"
                            class="rounded-full px-4 py-2 w-full text-black"
                        />
                        <select
                            name="selection"
                            class="rounded-full px-4 py-2 w-full text-black"
                            required
                        >
                            <option value="">Chọn nhu cầu học *</option>
                            <option value="Giao tiếp">Giao tiếp</option>
                            <option value="Luyện thi TOPIK">Luyện thi TOPIK</option>
                            <option value="Du học">Du học</option>
                        </select>
                    </div>

                    <textarea
                        name="message"
                        placeholder="Lời nhắn nếu có"
                        class="rounded-[25px] !px-4 !py-2.5 w-full text-black h-24 resize-none"
                    ></textarea>

                    <div class="flex justify-center">
                        <button
                            type="submit"
                            id="submit-btn"
                            class="w-full xl:w-auto text-2xl leading-[1.1] bg-gradient-to-r from-yellow-50 to-amber-300 rounded-[116.46px] shadow-[0px_3.938169002532959px_29.53626823425293px_0px_rgba(26,41,136,0.15)] border-l-2 border-r-2 border-b-2 border-white text-[#0057E4] py-2 px-5 font-semibold mx-auto transition-all duration-300"
                        >
                            <span class="btn-text">NHẬN TƯ VẤN</span>
                            <span class="btn-loading hidden">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-current inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Đang gửi...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer Dahee -->
    <footer
        class="bg-white border-t border-gray-200 py-6 lg:py-10 px-4 md:px-8 lg:px-16 text-sm text-gray-700"
    >
        <div class="container mx-auto !px-0 md:!px-8 xl:!px-0 xl:max-w-6xl">
            <div class="flex flex-col lg:flex-row lg:items-center mb-5">
                <!-- Logo + Description -->
                <div class="flex flex-col justify-center items-center space-y-4 lg:w-1/3">
                    <img
                        src="{{ asset('assets/frontend/dahee/assets/images/logo-footer.png') }}"
                        alt="Dahee Logo"
                        class="w-32 h-auto"
                    />
                </div>

                <!-- Hotline + Email -->
                <div
                    class="flex flex-col md:flex-row space-y-4 md:space-y-0 items-start justify-between flex-1 text-base"
                >
                    <div class="flex items-start space-x-3">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/telephone.svg') }}"
                            width="46"
                            height="46"
                            alt=""
                            class="size-[46px]"
                        />
                        <div>
                            <p class="font-normal">Hotline tư vấn (Zalo):</p>
                            <a href="tel:0983903074" class="text-[#0057E4] font-semibold"
                                >************</a
                            >
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <img
                            src="{{ asset('assets/frontend/dahee/assets/images/email.svg') }}"
                            width="46"
                            height="46"
                            alt=""
                            class="size-[46px]"
                        />
                        <div>
                            <p class="font-normal">Email:</p>
                            <a
                                href="mailto:<EMAIL>"
                                class="text-[#0057E4] font-semibold"
                                ><EMAIL></a
                            >
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col lg:flex-row gap-5 lg:gap-10 border-t border-blue-100 pt-8 text-[#313131] text-base leading-5"
            >
                <div class="lg:w-2/3 w-full text-base">
                    <p>
                        <strong>Học tiếng Hàn online cùng Dahee</strong> | Luyện thi TOPIK
                        từ cơ bản đến nâng cao
                    </p>
                    <p class="mt-2">
                        Tiếng Hàn Dahee là trung tâm tiếng Hàn online uy tín, chuyên đào tạo
                        học viên ôn thi TOPIK từ cấp độ sơ cấp đến cao cấp. Khóa học phù hợp
                        cho sinh viên đại học, người đi làm, học sinh cấp 3 muốn thi THPT
                        bằng tiếng Hàn hoặc du học Hàn Quốc.
                    </p>
                </div>
                <!-- Social + Connect -->
                <div class="space-y-4 flex-1">
                    <p>
                        Kết nối và nhận thêm thật nhiều kiến thức về Tiếng Hàn cùng Dahee
                        tại:
                    </p>
                    <div class="flex space-x-2.5 lg:space-x-4 text-gray-600 text-lg">
                        <a
                            href="https://www.facebook.com/tienghandahee"
                            target="_blank"
                            class="hover:text-blue-600 border border-gray-200 rounded-full size-[40px] flex items-center justify-center"
                        >
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a
                            href="https://www.tiktok.com/@tienghandahee?_t=ZS-8xiOM9NRRyR&_r=1"
                            target="_blank"
                            class="hover:text-black border border-gray-200 rounded-full size-[40px] flex items-center justify-center"
                        >
                            <i class="bi bi-tiktok"></i>
                        </a>
                        <a
                            href="https://www.youtube.com/@TiengHanDahee"
                            target="_blank"
                            class="hover:text-red-600 border border-gray-200 rounded-full size-[40px] flex items-center justify-center"
                        >
                            <i class="bi bi-youtube"></i>
                        </a>
                        <a
                            href="https://www.instagram.com/tienghandahee/"
                            target="_blank"
                            class="hover:text-pink-500 border border-gray-200 rounded-full size-[40px] flex items-center justify-center"
                        >
                            <i class="bi bi-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modal Feedback -->
    <div
        id="feedback-modal"
        class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center z-[99999]"
    >
        <div
            class="bg-white rounded-lg p-6 max-w-[500px] w-full mx-4 max-h-[90vh] overflow-hidden"
        >
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl text-[#0057E4]">Feedback từ học viên</h3>
                <button
                    id="close-modal"
                    class="text-gray-500 hover:text-gray-700 text-3xl font-bold"
                >
                    &times;
                </button>
            </div>
            <!-- Loading indicator -->
            <div id="feedback-loading" class="flex items-center justify-center h-[400px]">
                <div class="flex flex-col items-center">
                    <div
                        class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#0057E4] mb-4"
                    ></div>
                    <p class="text-gray-600">Đang tải feedback...</p>
                </div>
            </div>
            <div class="feedback-slider" style="display: none">
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/1.jpg') }}"
                        alt="Feedback 1"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/2.jpg') }}"
                        alt="Feedback 2"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/3.jpg') }}"
                        alt="Feedback 3"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/4.jpg') }}"
                        alt="Feedback 4"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/5.jpg') }}"
                        alt="Feedback 5"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/6.jpg') }}"
                        alt="Feedback 6"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/7.jpg') }}"
                        alt="Feedback 7"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/8.jpg') }}"
                        alt="Feedback 8"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/9.jpg') }}"
                        alt="Feedback 9"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/10.jpg') }}"
                        alt="Feedback 10"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/11.jpg') }}"
                        alt="Feedback 11"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
                <div class="feedback-item">
                    <img
                        data-lazy="{{ asset('assets/frontend/dahee/assets/images/feedback/12.jpg') }}"
                        alt="Feedback 12"
                        class="w-full max-h-[500px] object-contain h-auto rounded-lg feedback-image"
                    />
                </div>
            </div>
        </div>
    </div>
</main>


@include('partials.modals.register_login')



@stop
