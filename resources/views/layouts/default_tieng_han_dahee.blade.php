    @php $current_route_name = Route::currentRouteName(); @endphp
@php

    if (session('home')) {
        $home_page = App\Models\Builder_page::where('id', session('home'))->firstOrNew();
    } else {
        $home_page = App\Models\Builder_page::where('status', 1)->firstOrNew();
    }

    if(!empty($course_details->builder_ids)){
        $page_id = $course_details->builder_ids;
    }else{
        $page_id = $home_page->id;
    }
    $page = App\Models\Builder_page::find($page_id);
    $disable_bootstrap = false;
    if(!empty($page->disable_bootstrap)){
        $disable_bootstrap = $page->disable_bootstrap;
    }

@endphp
    <!DOCTYPE html>
<html lang="en">
<head>
@include('layouts.seo')
@stack('meta')

    <!-- fav icon -->
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}"/>

    
    <!-- Custom Fonts -->
    <link
        rel="stylesheet"
        href="https://elearning.topid.vn/assets/frontend/default/css/custome-front/custom-fronts.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet"
    />

    <!-- owl carousel -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/owl.carousel.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/owl.theme.default.min.css') }}">

    <!-- Jquery Ui Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/jquery-ui.css') }}">

    <!-- Nice Select Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/nice-select.css') }}">

    <!-- Fontawasome Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/all.min.css') }}">

    {{-- New Css Link --}}
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/swiper/swiper-bundle.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/slick/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/slick/slick-theme.css') }}">


    <!-- Custom Fonts -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/custome-front/custom-fronts.css') }}">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"/>

    <!-- Jquery Js -->
    <script src="{{ asset('assets/frontend/default/js/jquery-3.7.1.min.js') }}"></script>
    
    <link rel="stylesheet" href="{{ asset('assets/global/global.css') }}"/>
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/bootstrap/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/modal-auth.css') }}">
    <!--them-->
    <link rel="stylesheet" href="{{ asset('assets/frontend/dahee/assets/css/styles.css') }}">
    
    <!-- Custom Scripts (Head) -->
    <script src="https://cdn.tailwindcss.com"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    spacing: {
                        15: "60px",
                        18: "72px",
                        22: "88px",
                    },
                    maxWidth: {
                        "6xl": "1140px",
                    },
                },
            },
            plugins: [],
        };
    </script>

    @stack('css')

    @if (get_frontend_settings('recaptcha_status'))
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
<!-- Custom Scripts (Head) -->
    {!! get_settings('custom_script_head') !!}
</head>

<body>
<!-- Custom Scripts (Body Start) -->
{!! get_settings('custom_script_body_start') !!}
@yield('content')

@if(!$disable_bootstrap)
    <!-- Bootstrap Js -->
    {{--    <script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>--}}
@endif



<!-- Jquery Ui Js -->
<script src="{{ asset('assets/frontend/default/js/jquery-ui.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/js/jquery-ui.min.js') }}"></script>

<!-- nice select js -->
<script src="{{ asset('assets/frontend/default/js/jquery.nice-select.min.js') }}"></script>

{{-- New Js Link  --}}
<script src="{{ asset('assets/frontend/default/vendors/swiper/swiper-bundle.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/counterup/jquery.counterup.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/counterup/jquery.waypoints.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/slick/slick.min.js') }}"></script>

<script src="{{ asset('assets/frontend/default/vendors/flatpickr/flatpickr.min.js') }}"></script>

<!-- owl carousel js -->
<script src="{{ asset('assets/frontend/default/js/owl.carousel.min.js') }}"></script>


<!-- Player Js -->
<script src="{{ asset('assets/frontend/default/js/plyr.js') }}"></script>


<!-- Yaireo Tagify -->
<script src="{{ asset('assets/global/tagify-master/dist/tagify.min.js') }}"></script>


<!-- price range Js -->
<script src="{{ asset('assets/frontend/default/js/price_range_script.js') }}"></script>


<!-- Main Js -->
<script src="{{ asset('assets/frontend/default/js/script.js') }}"></script>


<script src="https://www.google.com/recaptcha/api.js" async defer></script>
{{--Theme--}}

<script src="{{ asset('assets/frontend/default/popper.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/bootstrap/bootstrap.min.js') }}"></script>

<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

@stack('js')

<script>
    AOS.init({
        duration: 650,
        once: true,
    });

    document.addEventListener("DOMContentLoaded", function () {
        // Get the header element
        const header = document.querySelector(".header");
        let headerHeight = header.offsetHeight; // Get the header height
        let ticking = false; // For scroll performance

        // Function to handle scroll events
        function handleScroll() {
            const currentScrollTop = window.scrollY;

            // Add or remove sticky class based on scroll position
            if (currentScrollTop > 50) {
                // If scrolling down past threshold and header is not already sticky
                if (!header.classList.contains("sticky-header")) {
                    // Remove returning class if it exists
                    header.classList.remove("returning");
                    // Add sticky class
                    header.classList.add("sticky-header");
                }
            } else {
                // If at top or scrolling back up to top
                if (header.classList.contains("sticky-header")) {
                    // Add returning class for animation
                    header.classList.add("returning");

                    // Wait for animation to complete before removing sticky
                    setTimeout(() => {
                        header.classList.remove("sticky-header");
                        header.classList.remove("returning");
                    }, 100); // Match this with the animation duration
                }
            }

            // Keep the scrolled class for visual changes (if needed)
            if (currentScrollTop > 50) {
                header.classList.add("scrolled");
            } else {
                header.classList.remove("scrolled");
            }

            ticking = false;
        }

        // Add scroll event listener with requestAnimationFrame for performance
        window.addEventListener("scroll", function () {
            if (!ticking) {
                window.requestAnimationFrame(function () {
                    handleScroll();
                });
                ticking = true;
            }
        });

        // Initial check
        handleScroll();

        // Recalculate header height on window resize
        window.addEventListener("resize", function () {
            headerHeight = header.offsetHeight;
            handleScroll();
        });

        // Mobile menu toggle functionality
        const menuToggle = document.querySelector(".menu-toggle");
        const mobileMenu = document.querySelector("#mobile-menu");

        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener("click", function () {
                mobileMenu.classList.toggle("hidden");
                header.classList.toggle("active");
            });
        }
    });

    $(window).on("load", function () {
        $(".teachers .teachers-cards-container").slick({
            prevArrow: $(".teachers-nav-btn.teachers-nav-prev"),
            nextArrow: $(".teachers-nav-btn.teachers-nav-next"),
            zIndex: 1,
            arrow: true,
            autoplay: true,
            dots: true,
            appendDots: $(".teachers-dots"),
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 5000,
            responsive: [
                {
                    breakpoint: 1280,
                    settings: {
                        slidesToShow: 2,
                    },
                },

                {
                    breakpoint: 480,
                    settings: {
                        centerMode: true,
                        centerPadding: "0",
                        slidesToShow: 1,
                    },
                },
            ],
        });

        $(".feedback-swiper").slick({
            prevArrow: $(".feedback-nav-btn.feedback-nav-prev"),
            nextArrow: $(".feedback-nav-btn.feedback-nav-next"),
            zIndex: 1,
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 3000,
            arrow: true,
            dots: true,
            autoplay: true,
            responsive: [
                {
                    breakpoint: 1280,
                    settings: {
                        slidesToShow: 2,
                    },
                },

                {
                    breakpoint: 480,
                    settings: {
                        centerMode: true,
                        centerPadding: "0",
                        slidesToShow: 1,
                    },
                },
            ],
        });
        $("[data-x-aos]").each(function name(i, element) {
            $(element).attr("data-aos", $(element).data("x-aos"));
        });

        AOS.refreshHard();
    });
</script>

<!-- Custom Scripts (Footer) -->

<script>
    // Mobile menu toggle functionality
    document.addEventListener("DOMContentLoaded", function () {
        const mobileMenuButton = document.getElementById("mobile-menu-button");
        const mobileMenu = document.getElementById("mobile-menu");

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener("click", function () {
                mobileMenu.classList.toggle("hidden");

                // Toggle hamburger icon
                const icon = mobileMenuButton.querySelector("svg");
                if (mobileMenu.classList.contains("hidden")) {
                    // Show hamburger icon
                    icon.innerHTML =
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                } else {
                    // Show close icon
                    icon.innerHTML =
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                }
            });
        }
    });
</script>

<!-- Feedback Modal Script -->
<script>
    $(document).ready(function () {
        let sliderInitialized = false;
        let imagesLoaded = 0;
        const totalImages = $(".feedback-image").length;

        // Hàm lazy load ảnh
        function lazyLoadImage(img) {
            return new Promise((resolve, reject) => {
                const $img = $(img);
                const src = $img.attr("data-lazy");

                if (src) {
                    const imageObj = new Image();

                    imageObj.onload = function () {
                        $img.attr("src", src);
                        $img.removeAttr("data-lazy");
                        resolve();
                    };

                    imageObj.onerror = function () {
                        reject();
                    };

                    imageObj.src = src;
                } else {
                    resolve();
                }
            });
        }

        // Hàm khởi tạo slider
        function initializeSlider() {
            if (!sliderInitialized) {
                $(".feedback-slider").slick({
                    dots: true,
                    infinite: true,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 3000,
                    arrows: true,
                    dots: false,
                    prevArrow: '<button type="button" class="slick-prev">❮</button>',
                    nextArrow: '<button type="button" class="slick-next">❯</button>',
                    lazyLoad: "ondemand",
                    // Lazy load ảnh khi slide thay đổi
                    beforeChange: function (event, slick, currentSlide, nextSlide) {
                        const $nextSlide = $(slick.$slides[nextSlide]);
                        const $img = $nextSlide.find(".feedback-image[data-lazy]");
                        if ($img.length > 0) {
                            lazyLoadImage($img[0]);
                        }
                    },
                });
                sliderInitialized = true;
            }
        }

        // Mở modal khi click nút "Xem thêm Feedback"
        $("#feedback-btn").click(function () {
            $("#feedback-modal").removeClass("hidden");
            $("body").addClass("overflow-hidden");

            // Hiển thị loading và ẩn slider
            $("#feedback-loading").show();
            $(".feedback-slider").hide();

            // Reset counter
            imagesLoaded = 0;

            // Load ảnh đầu tiên và một vài ảnh tiếp theo
            const firstImages = $(".feedback-image[data-lazy]").slice(0, 3);
            const loadPromises = [];

            firstImages.each(function () {
                loadPromises.push(
                    lazyLoadImage(this)
                        .then(() => {
                            imagesLoaded++;
                        })
                        .catch(() => {
                            imagesLoaded++;
                            console.warn(
                                "Failed to load image:",
                                $(this).attr("data-lazy"),
                            );
                        }),
                );
            });

            // Khi tất cả ảnh đầu tiên đã load xong
            Promise.all(loadPromises).then(() => {
                // Ẩn loading và hiển thị slider
                $("#feedback-loading").hide();
                $(".feedback-slider").show();

                // Khởi tạo slider
                initializeSlider();

                // Tiếp tục load các ảnh còn lại trong background
                const remainingImages = $(".feedback-image[data-lazy]");
                remainingImages.each(function () {
                    lazyLoadImage(this);
                });
            });
        });

        // Đóng modal khi click nút X
        $("#close-modal").click(function () {
            $("#feedback-modal").addClass("hidden");
            $("body").removeClass("overflow-hidden");
        });

        // Đóng modal khi click vào overlay
        $("#feedback-modal").click(function (e) {
            if (e.target === this) {
                $("#feedback-modal").addClass("hidden");
                $("body").removeClass("overflow-hidden");
            }
        });

        // Đóng modal khi nhấn ESC
        $(document).keydown(function (e) {
            if (e.keyCode === 27) {
                // ESC key
                $("#feedback-modal").addClass("hidden");
                $("body").removeClass("overflow-hidden");
            }
        });
    });
</script>
</body>

</html>
