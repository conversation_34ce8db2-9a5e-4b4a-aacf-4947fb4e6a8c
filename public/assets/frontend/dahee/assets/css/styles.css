html,
body {
    font-family: "Kanit", sans-serif;
    font-size: 16px;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Body padding to account for fixed header */
body {
    padding-top: 0;
    width: 100%;
    overflow-x: hidden;
}

body.header-fixed {
    padding-top: 80px;
}

.gradient-text {
    background-image: linear-gradient(90deg, #406bf2 0%, #003eb0 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    transform: translateY(0);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header.returning {
    transform: translateY(-100%);
}

@media (min-width: 1024px) {
    .header.scrolled {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }
}

/* Logo hover effect */
.header img {
    transition: transform 0.3s ease;
}

.header img:hover {
    transform: scale(1.05);
}

/* Navigation links */
.header nav a:not(.cta-button) {
    position: relative;
    text-decoration: none;
}

.header nav a:not(.cta-button)::after {
    content: "";
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 50%;
    background-color: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.header nav a:hover::after {
    width: 100%;
}

/* Enhanced CTA Button Styles */
.header .cta-button {
    width: 176px;
    height: 37px;
    padding-left: 37px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0px 3.157px 11.839px 0px rgba(26, 41, 136, 0.3);
}

.header .cta-button > div:first-child {
    height: calc(100% + 2px);
}

@media (min-width: 1024px) {
    .header .cta-button {
        width: 223px;
        height: 47px;
        padding-left: 24px;
        box-shadow: 0px 4px 15px 0px rgba(26, 41, 136, 0.3);
    }
}

.header .cta-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.25);
    background: linear-gradient(135deg, #e0f7fa 0%, #b3e5fc 100%);
    border-color: rgba(59, 130, 246, 0.3);
}

.header .cta-button:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

/* Mobile menu animation */
.header #mobile-menu {
    transition: all 0.3s ease;
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

.header #mobile-menu:not(.hidden) {
    opacity: 1;
    max-height: 500px;
}

/* Mobile menu links */
.header #mobile-menu a {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    padding-left: 1rem;
}

.header #mobile-menu a:hover {
    border-left-color: white;
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    .header .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .header nav {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
}

/* Hero Section Styles */
.hero {
    position: relative;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    overflow: hidden;
    padding-top: 80px;
}

.hero .hero-cta-btn {
    width: 274px;
    height: 47px;
    padding-left: 47px;
    box-shadow: 0px 3.938px 29.536px 0px rgba(26, 41, 136, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero .hero-cta-btn > div:first-child {
    height: calc(100% + 2px);
}

.hero .hero-cta-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25),
        0 8px 25px rgba(59, 130, 246, 0.4);
}

.hero .hero-cta-btn:active {
    transform: translateY(0) scale(0.98);
}

/* Floating Animation for Hero Elements */
@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.hero .animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Pulse Animation Enhancement */
@keyframes pulse-glow {
    0%,
    100% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        transform: scale(1.02);
    }
}

.hero .animate-pulse {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Bounce Animation Enhancement */
@keyframes bounce-soft {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}

.hero .animate-bounce {
    animation: bounce-soft 2s ease-in-out infinite;
}

/* Title Background Styling */
.hero .title-bg {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

/* Student Avatar Enhancements */
.hero .student-avatar {
    transition: transform 0.3s ease;
}

.hero .student-avatar:hover {
    transform: scale(1.1);
    z-index: 10;
}

@media (max-width: 768px) {
    .hero {
        padding-top: 80px;
    }

    .hero .hero-cta-btn {
        justify-content: center;
        padding: 1.25rem 2rem;
        padding-left: 47px;
    }

    .hero .training-tags {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero .student-avatars {
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .hero .title-bg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .hero p {
        font-size: 1.125rem;
    }
}

/* Wave Animation */
@keyframes wave {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100px);
    }
}

.hero .wave-animation {
    animation: wave 10s linear infinite;
}

/* Text Glow Effect */
.hero .text-glow {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* About Section Styles */
.about {
    position: relative;
}

.about .section-tag {
    position: relative;
}

.about h2 {
    position: relative;
}

.about .feature-card {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    padding: 8px 8px 10px 6px;
}

.about .feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transition: left 0.5s;
}

.about .feature-card:hover::before {
    left: 100%;
}

.about .feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.about .cta-section {
    position: relative;
    overflow: hidden;
}

.about .cta-badge {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0);
    transition: all 0.3s ease;
}

.about .cta-badge:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* Enhanced Animations */
@keyframes float-gentle {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.about .animate-bounce {
    animation: float-gentle 3s ease-in-out infinite;
}

@keyframes pulse-soft {
    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    }
}

.about .animate-pulse {
    animation: pulse-soft 2s ease-in-out infinite;
}

/* Text Enhancement */
.about .text-enhanced {
    line-height: 1.7;
    color: #374151;
}

.about .text-enhanced strong {
    color: #1f2937;
    font-weight: 600;
}

/* Hover Effects for Interactive Elements */
.about .interactive-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.about .interactive-element:hover {
    transform: translateY(-2px);
}

/* Course Combo Section Styles */
.course-combo {
    position: relative;
}

.course-combo .combo-bg-pattern {
    z-index: 1;
}

.course-combo .combo-title {
    position: relative;
}

.course-combo .combo-highlight {
    position: relative;
}

.course-combo .combo-highlight::after {
    content: "";
    position: absolute;
    right: 0;
    height: 12px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 2px;
    bottom: -20px;
}

.course-combo .combo-features .feature-item {
    transition: all 0.2s ease;
}

.course-combo .combo-features .feature-item:hover {
    transform: translateX(4px);
    color: #3b82f6;
}

.course-combo .price-sessions {
    background-image: url(../images/course-session-bg.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    min-width: 104px;
}

.course-combo .combo-cta-btn {
    position: relative;
    overflow: hidden;
    font-weight: 600;
    border: 1px solid #0057e4;
}

.course-combo .combo-cta-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-combo .combo-cta-btn:hover::before {
    left: 0;
}

/* Bottom Section */
.course-combo .combo-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-combo .combo-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-combo .combo-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-combo .combo-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-combo .combo-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-combo .combo-bottom-section {
        padding: 2rem;
    }

    .course-combo .combo-bottom-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 640px) {
    .course-combo .combo-bottom-section {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes combo-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.course-combo .combo-card {
    display: flex;
    flex-direction: column;
}

.course-combo .combo-card > div:last-child {
    flex: 1 1 auto;
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .course-title {
    position: relative;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

.course-list .course-subtitle {
    line-height: 1.3;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-price .price-main {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.course-list .course-price .price-sessions {
    background-image: url(../images/course-session-bg.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 640px) {
    .course-list .course-price .price-main {
        font-size: 2rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-list .course-price .price-sessions {
    animation: course-pulse 2s ease-in-out infinite;
}

/* Reason Section Styles */
.reason {
    position: relative;
}

.reason .reason-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    padding: 0.25rem;
}

.reason .reason-card:hover {
    transform: translateY(-8px);
    /* box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15); */
}

.reason .reason-icon {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    position: relative;
    z-index: 2;
}

.reason .reason-card:hover .reason-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.reason .reason-title {
    position: relative;
    z-index: 2;
}

.reason .reason-list {
    position: relative;
    z-index: 2;
}

.reason .reason-list li {
    transition: all 0.2s ease;
    position: relative;
}

.reason .reason-list li::marker {
    color: currentColor;
}

.reason .reason-card:hover .reason-list li {
    transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .reason .reason-card {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .reason .reason-card {
        padding: 1.25rem;
    }

    .reason .reason-icon {
        width: 1.75rem;
        height: 1.75rem;
    }
}

@media (max-width: 640px) {
    .reason .reason-card {
        padding: 1rem;
    }
}

@keyframes reason-glow {
    0%,
    100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
    }
}

.reason .reason-icon {
    animation: reason-glow 3s ease-in-out infinite;
}
.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}
/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s ease-in-out infinite;
}

@keyframes course-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.course-combo .combo-card:hover {
    animation: combo-float 2s ease-in-out infinite;
}

.course-combo .combo-badge {
    animation: pulse 2s infinite;
}

@keyframes combo-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Course List Section Styles */
.course-list {
    position: relative;
}

.course-list .course-bg-pattern {
    z-index: 1;
}

.course-list .combo-highlight::after {
    content: "";
    position: absolute;
    height: 10px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    right: -107px !important;
    bottom: -20px;
    width: 90px !important;
    height: 30px !important;
}

/* Course Cards */
.course-list .course-card {
    position: relative;
    overflow: hidden;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
}

.course-list .course-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 2;
}

.course-list .course-card:hover::before {
    left: 100%;
}

.course-list .course-card-title {
    color: #374151;
    font-weight: 700;
}

.course-list .course-cta-btn {
    border: 1px solid #0057e4;
}

.course-list .course-cta-btn.bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.course-list .course-cta-btn.bg-white {
    border-color: #3b82f6;
}

.course-list .course-cta-btn.bg-white::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-cta-btn.bg-white:hover::before {
    left: 0;
}

/* Bottom Section */
.course-list .course-bottom-section {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.course-list .course-bottom-title {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-list .course-view-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.course-list .course-view-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transition: left 0.3s ease;
    z-index: -1;
}

.course-list .course-view-more-btn:hover::before {
    left: 0;
}

@media (max-width: 768px) {
    .course-list {
        padding: 3rem 0;
    }

    .course-list .course-bottom-title {
        font-size: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes course-float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.course-list .course-card:hover {
    animation: course-float 2s;
}

/* Teachers Section Styles */
.teachers {
    position: relative;
    background: radial-gradient(
        70.6% 63.16% at 50% 36.84%,
        #fff 0%,
        #cfe1ff 100%
    );
}

.teachers .teachers-title {
    text-shadow: 0 2px 4px rgba(10, 74, 255, 0.1);
    position: relative;
}

.teachers .combo-highlight {
    position: relative;
}

.teachers .combo-highlight::after {
    content: "";
    position: absolute;
    height: 12.5px;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 2px;
    bottom: -26px;
}
#feedback .combo-highlight::after {
    right: 90px;
    bottom: -20px;
}

/* Carousel Styles */

.teachers .teachers-cards-container {
    transform: translateX(0);
}

/* Teacher Cards */
.teachers .teachers-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.teachers .teachers-card:hover {
    transform: translateY(-10px);
}

.teachers .teachers-avatar {
    transition: transform 0.3s ease;
    border: 5px solid #fff;
    box-shadow: 0px 4px 0px 0px #3b92ff;
}

.teachers .teachers-card:hover .teachers-avatar {
    transform: translateY(-20px);
    border-color: #3b82f6;
}

.teachers .teachers-name {
    transition: color 0.3s ease;
    position: relative;
    z-index: 2;
}

.teachers .teachers-card:hover .teachers-name {
    color: #164bba;
}

.teachers .teachers-badge {
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    width: 160px;
}

.teachers .teachers-card:hover .teachers-badge {
    color: white;
    transform: scale(1.05);
}

.teachers .teachers-section-title {
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.teachers .teachers-card:hover .teachers-section-title {
    color: #1e40af;
}

/* Carousel Dots */
.teachers .teachers-dots {
    position: relative;
}

.teachers .teachers-dots .slick-dots li.slick-active button {
    background: #3b92ff;
}

.teachers .teachers-dots .slick-dots li button:before {
    display: none;
}

.teachers .teachers-dots .slick-dots li {
    width: 10px;
    height: 10px;
}

.teachers .teachers-dots .slick-dots li button {
    width: 10px;
    height: 10px;
    border-radius: 99999px;
    background-color: #fff;
}

.teachers .teachers-dot {
    transition: all 0.3s ease;
}

.teachers .teachers-dot:hover {
    transform: scale(1.2);
}

.teachers .teachers-dot.active {
    background-color: #3b82f6;
    transform: scale(1.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .teachers .teachers-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .teachers .teachers-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 640px) {
    .teachers .teachers-title {
        font-size: 1.75rem;
        line-height: 1.2;
    }
}

@keyframes teachers-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Interactive Elements */
.teachers .teachers-card {
    cursor: pointer;
}

.teachers .teachers-card:active {
    transform: translateY(-5px) scale(0.98);
}

.teachers .teachers-dot:active {
    transform: scale(1.1);
}
.testimonial-card::after {
    content: "";
    position: absolute;
    bottom: 2px;
    right: -9px; /* Positioned towards the bottom-right */
    transform: translate(-50%, 100%);
    width: 0;
    height: 0;
    border-left: 0px solid transparent;
    border-right: 36px solid transparent;
    border-top: 27px solid white;
}

/* Custom styles for Swiper pagination bullets */
.swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background-color: #ffffff;
    opacity: 0.5;
    transition: opacity 0.3s, transform 0.3s;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
}

#feedback .slick-dots li {
    background: #fff;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

#feedback .slick-dots li.slick-active {
    background: #ffca43;
}
.combo-highlight {
    position: relative;
    display: inline-block !important;
}

.combo-highlight::after {
    content: "";
    position: absolute;
    background-image: url(../images/highlight-underline.svg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 2px;
    right: 25px;
    bottom: -24px;
    width: 135px !important;
    height: 20px !important;
}

.txt-gra-hv {
    background: var(
        --Gradient-1,
        linear-gradient(270deg, #bff2ff 0%, #fff 47.6%, #fffbab 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

#feedback-modal .slick-prev:before,
#feedback-modal .slick-next:before {
    font-size: 35px !important;
    color: #0d6efd;
    z-index: 999999;
    position: relative;
}

#feedback-modal .slick-prev,
#feedback-modal .slick-next {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 999999;
}
.swiper-container .slick-dots li button:before {
    display: none;
}
#commitment .combo-highlight::after {
    right: 60px;
    bottom: -20px;
}
#form-register .combo-highlight::after {
    bottom: -30px;
}
