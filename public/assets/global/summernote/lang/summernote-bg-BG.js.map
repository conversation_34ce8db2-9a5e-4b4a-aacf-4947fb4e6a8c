{"version": 3, "file": "lang/summernote-bg-BG.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,UADF;AAEJC,QAAAA,MAAM,EAAE,UAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,UALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,SAAS,EAAE,cARP;AASJC,QAAAA,WAAW,EAAE,cATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,aADF;AAELC,QAAAA,MAAM,EAAE,kBAFH;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,aAAa,EAAE,eALV;AAMLC,QAAAA,SAAS,EAAE,iBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,kBARN;AASLC,QAAAA,YAAY,EAAE,iBATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,2BAbV;AAcLC,QAAAA,SAAS,EAAE,+BAdN;AAeLC,QAAAA,eAAe,EAAE,eAfZ;AAgBLC,QAAAA,eAAe,EAAE,4BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,uCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,0BAlBA;AAmBLC,QAAAA,MAAM,EAAE,sBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,iBAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,oBALX;AAMJT,QAAAA,GAAG,EAAE,WAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,mBAFR;AAGLC,QAAAA,WAAW,EAAE,mBAHR;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,WAAW,EAAE,uBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,UAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,YALC;AAMLC,QAAAA,EAAE,EAAE,YANC;AAOLC,QAAAA,EAAE,EAAE,YAPC;AAQLC,QAAAA,EAAE,EAAE,YARC;AASLC,QAAAA,EAAE,EAAE,YATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,iBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,cAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,uBAFA;AAGTC,QAAAA,MAAM,EAAE,OAHC;AAITC,QAAAA,IAAI,EAAE,qBAJG;AAKTC,QAAAA,MAAM,EAAE,QALC;AAMTC,QAAAA,KAAK,EAAE,sBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,uBADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,cAAc,EAAE,qBANX;AAOLC,QAAAA,KAAK,EAAE,YAPF;AAQLC,QAAAA,cAAc,EAAE,yBARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA9FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,qBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,uBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,yBALb;AAMRC,QAAAA,aAAa,EAAE,mBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP3B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,iBADf;AAEJ,gBAAQ,2BAFJ;AAGJ,gBAAQ,0BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,SANJ;AAOJ,kBAAU,uBAPN;AAQJ,qBAAa,qBART;AASJ,yBAAiB,wBATb;AAUJ,wBAAgB,iBAVZ;AAWJ,uBAAe,qBAXX;AAYJ,yBAAiB,wBAZb;AAaJ,wBAAgB,sBAbZ;AAcJ,uBAAe,yBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,6BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-bg-BG.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'bg-BG': {\n      font: {\n        bold: 'Удебелен',\n        italic: 'Наклонен',\n        underline: 'Подчертан',\n        clear: 'Изчисти стиловете',\n        height: 'Вис<PERSON><PERSON>и<PERSON>',\n        name: 'Шриф<PERSON>',\n        strikethrough: 'Задраскано',\n        subscript: 'Долен индекс',\n        superscript: 'Горен индекс',\n        size: 'Размер на шрифта',\n      },\n      image: {\n        image: 'Изображение',\n        insert: 'Постави картинка',\n        resizeFull: 'Цял размер',\n        resizeHalf: 'Размер на 50%',\n        resizeQuarter: 'Размер на 25%',\n        floatLeft: 'Подравни в ляво',\n        floatRight: 'Подравни в дясно',\n        floatNone: 'Без подравняване',\n        shapeRounded: 'Форма: Заоблено',\n        shapeCircle: 'Форма: Кръг',\n        shapeThumbnail: 'Форма: Миниатюра',\n        shapeNone: 'Форма: Без',\n        dragImageHere: 'Пуснете изображението тук',\n        dropImage: 'Пуснете Изображение или Текст',\n        selectFromFiles: 'Изберете файл',\n        maximumFileSize: 'Максимален размер на файла',\n        maximumFileSizeError: 'Достигнат Максимален размер на файла.',\n        url: 'URL адрес на изображение',\n        remove: 'Премахни изображение',\n        original: 'Оригинал',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видео линк',\n        insert: 'Добави Видео',\n        url: 'Видео URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Връзка',\n        insert: 'Добави връзка',\n        unlink: 'Премахни връзка',\n        edit: 'Промени',\n        textToDisplay: 'Текст за показване',\n        url: 'URL адрес',\n        openInNewWindow: 'Отвори в нов прозорец',\n      },\n      table: {\n        table: 'Таблица',\n        addRowAbove: 'Добави ред отгоре',\n        addRowBelow: 'Добави ред отдолу',\n        addColLeft: 'Добави колона отляво',\n        addColRight: 'Добави колона отдясно',\n        delRow: 'Изтрии ред',\n        delCol: 'Изтрии колона',\n        delTable: 'Изтрии таблица',\n      },\n      hr: {\n        insert: 'Добави хоризонтална линия',\n      },\n      style: {\n        style: 'Стил',\n        p: 'Нормален',\n        blockquote: 'Цитат',\n        pre: 'Код',\n        h1: 'Заглавие 1',\n        h2: 'Заглавие 2',\n        h3: 'Заглавие 3',\n        h4: 'Заглавие 4',\n        h5: 'Заглавие 5',\n        h6: 'Заглавие 6',\n      },\n      lists: {\n        unordered: 'Символен списък',\n        ordered: 'Цифров списък',\n      },\n      options: {\n        help: 'Помощ',\n        fullscreen: 'На цял екран',\n        codeview: 'Преглед на код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Намаляване на отстъпа',\n        indent: 'Абзац',\n        left: 'Подравняване в ляво',\n        center: 'Център',\n        right: 'Подравняване в дясно',\n        justify: 'Разтягане по ширина',\n      },\n      color: {\n        recent: 'Последния избран цвят',\n        more: 'Още цветове',\n        background: 'Цвят на фона',\n        foreground: 'Цвят на шрифта',\n        transparent: 'Прозрачен',\n        setTransparent: 'Направете прозрачен',\n        reset: 'Възстанови',\n        resetToDefault: 'Възстанови оригиналните',\n        cpSelect: 'Изберете',\n      },\n      shortcut: {\n        shortcuts: 'Клавишни комбинации',\n        close: 'Затвори',\n        textFormatting: 'Форматиране на текста',\n        action: 'Действие',\n        paragraphFormatting: 'Форматиране на параграф',\n        documentStyle: 'Стил на документа',\n        extraKeys: 'Екстра бутони',\n      },\n      help: {\n        'insertParagraph': 'Добави Параграф',\n        'undo': 'Отмени последната промяна',\n        'redo': 'Върни последната промяна',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Удебели',\n        'italic': 'Приложи наклонен стил',\n        'underline': 'Приложи подчераване',\n        'strikethrough': 'Приложи зачеркнат стил',\n        'removeFormat': 'Изчисти стилове',\n        'justifyLeft': 'Подравняване в ляво',\n        'justifyCenter': 'Подравняване в центъра',\n        'justifyRight': 'Подравняване в дясно',\n        'justifyFull': 'Двустранно подравняване',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Вмъкни хоризонтално правило',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Назад',\n        redo: 'Напред',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Избери Специални символи',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}