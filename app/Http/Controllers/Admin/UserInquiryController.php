<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserInquiry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class UserInquiryController extends Controller
{
    /**
     * Hiển thị danh sách inquiry
     */
    public function index(Request $request)
    {
        // Đánh dấu tất cả đã đọc khi vào trang
        UserInquiry::unread()->update(['is_read' => true]);

        $query = UserInquiry::query();

        // Tìm kiếm
        if ($request->search) {
            $query->search($request->search);
        }

        // Sắp xếp theo thời gian mới nhất
        $inquiries = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.user_inquiry.index', compact('inquiries'));
    }

    /**
     * <PERSON><PERSON>n thị chi tiết inquiry
     */
    public function show($id)
    {
        $inquiry = UserInquiry::findOrFail($id);
        $inquiry->markAsRead();
        
        return view('admin.user_inquiry.show', compact('inquiry'));
    }

    /**
     * Xóa inquiry
     */
    public function destroy($id)
    {
        UserInquiry::findOrFail($id)->delete();
        
        Session::flash('success', 'Đã xóa thông tin thành công');
        return redirect()->back();
    }

    /**
     * API endpoint để nhận thông tin từ landing page (AJAX)
     */
    public function store(Request $request)
    {
        // Validation
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'selection' => 'nullable|string|max:255',
            'message' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng kiểm tra lại thông tin',
                'errors' => $validator->errors()
            ], 422);
        }

        // Chống spam - kiểm tra IP và thời gian gửi cuối
        $ip = $request->ip();
        $lastSubmission = UserInquiry::where('ip_address', $ip)
            ->where('created_at', '>', now()->subMinutes(5))
            ->first();

        if ($lastSubmission) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng chờ 5 phút trước khi gửi lại thông tin'
            ], 429);
        }

        // Tạo note từ selection và message
        $note = '';
        if ($request->selection) {
            $note .= 'Nhu cầu học: ' . $request->selection;
        }
        if ($request->message) {
            if ($note) $note .= "\n\n";
            $note .= 'Lời nhắn: ' . $request->message;
        }

        // Lưu thông tin
        UserInquiry::create([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'note' => $note,
            'ip_address' => $ip,
            'user_agent' => $request->userAgent(),
            'last_submission' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Cảm ơn bạn đã để lại thông tin. Chúng tôi sẽ liên hệ sớm nhất!'
        ]);
    }
}
