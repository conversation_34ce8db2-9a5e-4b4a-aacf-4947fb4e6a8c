<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Language_phrase;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class TranslateToVietnamese extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translate:vietnamese {--language_id=4 : The language ID to translate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translate all phrases with specified language_id to Vietnamese using OpenAI';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $languageId  = 4;
        $this->info("Starting translation of English phrases with language_id = {$languageId} to Vietnamese...");

        // Get phrases where translated field contains English (same as phrase field)
        $phrases = Language_phrase::where('language_id', $languageId)
                    ->whereColumn('translated', 'phrase')
                    ->orderBy('created_at', 'desc')
                    ->get();

        if ($phrases->isEmpty()) {
            $this->info("No untranslated phrases found for language_id = {$languageId}");
            return 0;
        }

        $this->info("Found {$phrases->count()} phrases to translate from English to Vietnamese.");

        $bar = $this->output->createProgressBar($phrases->count());
        $bar->start();

        $successCount = 0;
        $errorCount = 0;

        foreach ($phrases as $phrase) {
            try {
                // Translate the phrase
                $translated = $this->translateWithOpenAI($phrase->phrase);

                if ($translated) {
                    // Display original and translated text
                    $this->newLine();
                    $this->info("Original: " . $phrase->phrase);
                    $this->info("Translated: " . $translated);
                    $this->newLine();

                    // Update the phrase with the translation
                    $phrase->translated = $translated;
                    $phrase->updated_at = now();
                    $phrase->save();

                    $successCount++;
                } else {
                    $this->newLine();
                    $this->warn("Failed to translate phrase: {$phrase->phrase}");
                    $errorCount++;
                }

                // Sleep to avoid rate limiting
                usleep(500000); // 0.5 seconds

            } catch (\Exception $e) {
                $this->newLine();
                $this->error("Error translating phrase ID {$phrase->id}: {$e->getMessage()}");
                Log::error("Translation error: {$e->getMessage()}", ['phrase_id' => $phrase->id]);
                $errorCount++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        $this->info("Translation completed!");
        $this->info("Successfully translated: {$successCount} phrases");

        if ($errorCount > 0) {
            $this->warn("Failed to translate: {$errorCount} phrases");
        }

        return 0;
    }

    /**
     * Translate text to Vietnamese using custom GPT API
     *
     * @param string $text
     * @return string|null
     */
    private function translateWithOpenAI($text)
    {
        // Custom API endpoint and key
        $apiEndpoint = "https://api.v3.cm/v1/chat/completions";
        $apiKey = "sk-ig2AGaCPwwQG5Pyl43Ce07Ef1cE24e70A0D5706e214f32E9";

        $systemPrompt = "Bạn là một chuyên gia dịch thuật chuyên về hệ thống e-learning, công nghệ giáo dục, và phần mềm quản lý học tập. Nhiệm vụ của bạn là dịch văn bản từ tiếng Anh sang tiếng Việt một cách chính xác và tự nhiên, đồng thời đảm bảo các yếu tố sau:
1. Sử dụng thuật ngữ chuyên ngành e-learning và công nghệ giáo dục chính xác
2. Đảm bảo ngữ cảnh của từng chức năng được dịch phù hợp với môi trường học tập trực tuyến
3. Giữ nguyên các thuật ngữ kỹ thuật phổ biến không cần dịch (như dashboard, quiz, assignment...)
4. Đảm bảo câu văn tự nhiên, rõ ràng và dễ hiểu đối với người dùng Việt Nam
5. Thích ứng với ngữ cảnh giao diện người dùng nếu đang dịch các phần liên quan đến UI
6. Chỉ trả về bản dịch hoàn chỉnh, không thêm chú thích hoặc giải thích
7. Nếu không thể dịch chính xác, hãy trả về chuỗi rỗng
8. Withdraw sẽ là rút tiền
";


        $userPrompt = "Dịch đoạn văn bản sau sang tiếng Việt. Đây là nội dung từ hệ thống e-learning của chúng tôi, yêu cầu dịch chính xác về mặt ngữ nghĩa và bối cảnh sử dụng trong lĩnh vực công nghệ giáo dục và hệ thống quản lý học tập trực tuyến. Chỉ trả về bản dịch mà không thêm bất kỳ giải thích hay ghi chú nào:\n\n{$text}";

        $data = [
            "messages" => [
                [
                    "role" => "system",
                    "content" => $systemPrompt
                ],
                [
                    "role" => "user",
                    "content" => $userPrompt
                ]
            ],
            "stream" => false,
            "model" => "gpt-4o-2024-11-20",
            "temperature" => 0.3, // Giảm temperature để có kết quả nhất quán hơn
            "presence_penalty" => 0,
            "frequency_penalty" => 0,
            "top_p" => 1
        ];

        $ch = curl_init($apiEndpoint);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json",
            "Authorization: Bearer " . $apiKey
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            $this->warn("API request failed with HTTP code {$httpCode}: {$response}");
            return null;
        }

        if ($response) {
            $responseData = json_decode($response, true);
            if (isset($responseData['choices'][0]['message']['content'])) {
                return trim($responseData['choices'][0]['message']['content']);
            }
        }

        return null;
    }
}
