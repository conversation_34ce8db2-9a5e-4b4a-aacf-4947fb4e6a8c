<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserInquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'note',
        'ip_address',
        'user_agent',
        'last_submission',
        'is_read',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'last_submission' => 'datetime',
    ];

    /**
     * Scope để lấy các inquiry chưa đọc
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope để tìm kiếm
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('note', 'like', "%{$search}%");
        });
    }

    /**
     * Đ<PERSON>h dấu đã đọc
     */
    public function markAsRead()
    {
        $this->update(['is_read' => true]);
    }
}
